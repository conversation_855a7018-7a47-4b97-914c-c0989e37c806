#!/usr/bin/env python3
"""
Attribution Dashboard Customization Tool
A Python GUI application for analyzing, customizing, and deploying client-specific Attribution Dashboard instances.

Features:
- File analysis and validation
- Client-specific customization
- Location filtering management
- Code generation and deployment
- Branding and configuration management

Author: Attribution Dashboard Team
Version: 1.0.0
"""

import tkinter as tk
from tkinter import ttk, filedialog, messagebox, scrolledtext
import os
import sys
import json
import re
import shutil
from pathlib import Path
from datetime import datetime
from typing import Dict, List, Optional, Tuple, Any
import logging
from dataclasses import dataclass, asdict
import webbrowser
import tempfile
import threading
import time
import http.server
import socketserver
import socket
from urllib.parse import urljoin
import requests
try:
    from tkinter import html
    HTML_AVAILABLE = True
except ImportError:
    HTML_AVAILABLE = False

# Try to import webview for better HTML rendering
try:
    import webview
    WEBVIEW_AVAILABLE = True
except ImportError:
    WEBVIEW_AVAILABLE = False

# Configure logging with UTF-8 encoding for Windows compatibility
logging.basicConfig(
    level=logging.INFO,  # Back to normal logging
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('dashboard_customizer.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

# Airtable API Integration
class AirtableAPIManager:
    """Manages Airtable API operations for base and table management"""

    def __init__(self, api_token=None):
        self.api_token = api_token or os.getenv('AIRTABLE_API_KEY')
        self.base_url = "https://api.airtable.com/v0"
        self.meta_url = "https://api.airtable.com/v0/meta"
        self.headers = {
            "Authorization": f"Bearer {self.api_token}",
            "Content-Type": "application/json"
        }

    def test_connection(self):
        """Test Airtable API connection"""
        try:
            response = requests.get(
                f"{self.meta_url}/bases",
                headers=self.headers,
                timeout=10
            )

            if response.status_code == 200:
                data = response.json()
                bases_count = len(data.get('bases', []))

                return True, f"Connected successfully. Found {bases_count} accessible bases. Ready for table management."
            elif response.status_code == 401:
                return False, "Authentication failed. Please check your API token."
            else:
                return False, f"API Error: {response.status_code} - {response.text}"

        except requests.exceptions.RequestException as e:
            return False, f"Connection error: {str(e)}"



    def get_token_requirements_help(self):
        """Get help text for creating a proper API token"""
        return """
To create bases in Airtable, your API token needs specific scopes:

REQUIRED SCOPES:
✓ schema.bases:read - Read base schemas
✓ schema.bases:write - CREATE/MODIFY bases (REQUIRED for base creation)
✓ data.records:read - Read records
✓ data.records:write - Write records

WORKSPACE PERMISSIONS:
✓ You must be an OWNER or CREATOR in the workspace
✓ Collaborator access is NOT sufficient

HOW TO CREATE A PROPER TOKEN:
1. Go to https://airtable.com/create/tokens
2. Click "Create new token"
3. Give it a name like "Attribution Dashboard"
4. Add these scopes:
   - schema.bases:read
   - schema.bases:write  ← This is the key one!
   - data.records:read
   - data.records:write
5. Select the workspace where you want to create bases
6. Click "Create token"
7. Copy the token and paste it in this app

If base creation still fails after this, check:
- You're an owner/creator in the workspace
- The workspace allows base creation
- Your Airtable plan supports API base creation
"""

    def list_bases(self):
        """List all accessible Airtable bases"""
        try:
            response = requests.get(
                f"{self.meta_url}/bases",
                headers=self.headers,
                timeout=15
            )
            response.raise_for_status()

            data = response.json()
            bases = []

            for base in data.get('bases', []):
                base_info = {
                    'id': base.get('id'),
                    'name': base.get('name'),
                    'permission_level': base.get('permissionLevel'),
                    'created_time': base.get('createdTime'),
                    'tables_count': len(base.get('tables', []))
                }
                bases.append(base_info)

            logger.info(f"Found {len(bases)} accessible Airtable bases")
            return True, bases

        except requests.exceptions.RequestException as e:
            logger.error(f"Error listing bases: {str(e)}")
            return False, f"Error listing bases: {str(e)}"

    def get_base_schema(self, base_id):
        """Get detailed schema information for a specific base"""
        try:
            response = requests.get(
                f"{self.meta_url}/bases/{base_id}/tables",
                headers=self.headers,
                timeout=15
            )
            response.raise_for_status()

            data = response.json()
            tables = []

            for table in data.get('tables', []):
                table_info = {
                    'id': table.get('id'),
                    'name': table.get('name'),
                    'description': table.get('description', ''),
                    'primary_field_id': table.get('primaryFieldId'),
                    'fields': []
                }

                # Extract field information
                for field in table.get('fields', []):
                    field_info = {
                        'id': field.get('id'),
                        'name': field.get('name'),
                        'type': field.get('type'),
                        'description': field.get('description', ''),
                        'options': field.get('options', {})
                    }
                    table_info['fields'].append(field_info)

                tables.append(table_info)

            logger.info(f"Retrieved schema for base {base_id}: {len(tables)} tables")
            return True, tables

        except requests.exceptions.RequestException as e:
            logger.error(f"Error getting base schema: {str(e)}")
            return False, f"Error getting base schema: {str(e)}"

    def create_client_base(self, client_name, workspace_id=None):
        """Create a new Airtable base for a client"""
        try:
            # Note: Base creation via API requires specific permissions and may not be available
            # for all API tokens. This is a limitation of the Airtable API.

            # Prepare base creation payload - simplified for better compatibility
            payload = {
                "name": f"{client_name} - Attribution Dashboard"
            }

            # Add workspace if specified
            if workspace_id:
                payload["workspaceId"] = workspace_id

            # Try to create base without tables first (tables can be added later)
            response = requests.post(
                f"{self.meta_url}/bases",
                headers=self.headers,
                json=payload,
                timeout=30
            )

            if response.status_code == 200:
                data = response.json()
                base_info = {
                    'id': data.get('id'),
                    'name': data.get('name'),
                    'tables': []  # Will be empty initially
                }
                logger.info(f"Created new base: {base_info['name']} ({base_info['id']})")

                # Try to add tables to the created base
                try:
                    self._add_tables_to_base(base_info['id'])
                    # Refresh base info to get tables
                    success, tables = self.get_base_schema(base_info['id'])
                    if success:
                        base_info['tables'] = tables
                except Exception as table_error:
                    logger.warning(f"Base created but failed to add tables: {str(table_error)}")

                return True, base_info
            elif response.status_code == 422:
                # Common error - API token may not have base creation permissions
                error_msg = ("Base creation failed: Your API token may not have the required permissions. "
                           "Please ensure your token has 'schema.bases:write' scope, or create the base manually "
                           "in Airtable and use the 'Use Selected Base' option instead.")
                logger.error(f"Base creation failed (422): {response.text}")
                return False, error_msg
            elif response.status_code == 403:
                error_msg = ("Base creation not permitted: Your API token doesn't have permission to create bases. "
                           "Please create the base manually in Airtable and use the 'Use Selected Base' option.")
                logger.error(f"Base creation forbidden (403): {response.text}")
                return False, error_msg
            else:
                error_msg = f"Failed to create base: {response.status_code} - {response.text}"
                logger.error(f"Base creation failed: {error_msg}")
                return False, error_msg

        except requests.exceptions.RequestException as e:
            logger.error(f"Error creating base: {str(e)}")
            return False, f"Error creating base: {str(e)}"

    def _add_tables_to_base(self, base_id):
        """Add tables to an existing base (if supported by API token)"""
        # Note: This may require additional permissions
        tables_to_create = [
            self._get_ghl_table_template(),
            self._get_google_ads_table_template(),
            self._get_pos_table_template(),
            self._get_meta_ads_table_template()
        ]

        for table_template in tables_to_create:
            try:
                response = requests.post(
                    f"{self.meta_url}/bases/{base_id}/tables",
                    headers=self.headers,
                    json=table_template,
                    timeout=30
                )

                if response.status_code == 200:
                    logger.info(f"Added table: {table_template['name']}")
                else:
                    logger.warning(f"Failed to add table {table_template['name']}: {response.status_code}")

            except Exception as e:
                logger.warning(f"Error adding table {table_template['name']}: {str(e)}")
                continue

    def create_table_in_base(self, base_id, table_template):
        """Create a table in an existing base using the Airtable MCP approach"""
        try:
            # Use the direct API approach since we know it works
            response = requests.post(
                f"{self.meta_url}/bases/{base_id}/tables",
                headers=self.headers,
                json=table_template,
                timeout=30
            )

            if response.status_code == 200:
                table_data = response.json()
                logger.info(f"Created table: {table_template['name']} in base {base_id}")
                return True, table_data
            else:
                error_msg = f"Failed to create table: {response.status_code} - {response.text}"
                logger.error(error_msg)
                return False, error_msg

        except Exception as e:
            error_msg = f"Error creating table: {str(e)}"
            logger.error(error_msg)
            return False, error_msg

    def _get_ghl_table_template(self):
        """Get GHL table template with standard fields"""
        return {
            "name": "GHL Leads",
            "description": "GoHighLevel leads and contact data",
            "fields": [
                {
                    "name": "Contact Name",
                    "type": "singleLineText"
                },
                {
                    "name": "Email",
                    "type": "email"
                },
                {
                    "name": "Phone",
                    "type": "phoneNumber"
                },
                {
                    "name": "Date Created",
                    "type": "dateTime",
                    "options": {
                        "dateFormat": {"name": "us", "format": "M/D/YYYY"},
                        "timeFormat": {"name": "12hour", "format": "h:mma"},
                        "timeZone": "America/New_York"
                    }
                },
                {
                    "name": "Lead Source",
                    "type": "singleSelect",
                    "options": {
                        "choices": [
                            {"name": "Website"},
                            {"name": "Google Ads"},
                            {"name": "Facebook"},
                            {"name": "Referral"},
                            {"name": "Other"}
                        ]
                    }
                },
                {
                    "name": "Status",
                    "type": "singleSelect",
                    "options": {
                        "choices": [
                            {"name": "New"},
                            {"name": "Contacted"},
                            {"name": "Qualified"},
                            {"name": "Converted"},
                            {"name": "Lost"}
                        ]
                    }
                },
                {
                    "name": "Tags",
                    "type": "multipleSelects",
                    "options": {
                        "choices": [
                            {"name": "Hot Lead"},
                            {"name": "Follow Up"},
                            {"name": "VIP"}
                        ]
                    }
                },
                {
                    "name": "Notes",
                    "type": "multilineText"
                }
            ]
        }

    def _get_google_ads_table_template(self):
        """Get Google Ads table template with standard fields"""
        return {
            "name": "Google Ads",
            "description": "Google Ads campaign performance data",
            "fields": [
                {
                    "name": "Date",
                    "type": "date",
                    "options": {
                        "dateFormat": {"name": "us", "format": "M/D/YYYY"}
                    }
                },
                {
                    "name": "Campaign ID",
                    "type": "singleLineText"
                },
                {
                    "name": "Campaign Name",
                    "type": "singleLineText"
                },
                {
                    "name": "Cost",
                    "type": "currency",
                    "options": {
                        "precision": 2,
                        "symbol": "$"
                    }
                },
                {
                    "name": "Impressions",
                    "type": "number",
                    "options": {"precision": 0}
                },
                {
                    "name": "Clicks",
                    "type": "number",
                    "options": {"precision": 0}
                },
                {
                    "name": "Conversions",
                    "type": "number",
                    "options": {"precision": 0}
                },
                {
                    "name": "CTR",
                    "type": "percent",
                    "options": {"precision": 2}
                },
                {
                    "name": "CPC",
                    "type": "currency",
                    "options": {
                        "precision": 2,
                        "symbol": "$"
                    }
                },
                {
                    "name": "Conv. Rate",
                    "type": "percent",
                    "options": {"precision": 2}
                },
                {
                    "name": "Cost per Conv.",
                    "type": "currency",
                    "options": {
                        "precision": 2,
                        "symbol": "$"
                    }
                }
            ]
        }

    def _get_pos_table_template(self):
        """Get POS table template with standard fields"""
        return {
            "name": "POS",
            "description": "Point of Sale transaction and customer data",
            "fields": [
                {
                    "name": "Name",
                    "type": "singleLineText"
                },
                {
                    "name": "Company",
                    "type": "singleLineText"
                },
                {
                    "name": "Ticket Count",
                    "type": "number",
                    "options": {"precision": 0}
                },
                {
                    "name": "Ticket Amount",
                    "type": "currency",
                    "options": {
                        "precision": 2,
                        "symbol": "$"
                    }
                },
                {
                    "name": "Location",
                    "type": "singleSelect",
                    "options": {
                        "choices": [
                            {"name": "Main Location"},
                            {"name": "Branch 1"},
                            {"name": "Branch 2"}
                        ]
                    }
                },
                {
                    "name": "Phone",
                    "type": "phoneNumber"
                },
                {
                    "name": "Email",
                    "type": "email"
                },
                {
                    "name": "Created",
                    "type": "dateTime",
                    "options": {
                        "dateFormat": {"name": "us", "format": "M/D/YYYY"},
                        "timeFormat": {"name": "12hour", "format": "h:mma"},
                        "timeZone": "America/New_York"
                    }
                },
                {
                    "name": "Customer",
                    "type": "singleLineText"
                }
            ]
        }

    def _get_meta_ads_table_template(self):
        """Get Meta Ads table template with standard fields"""
        return {
            "name": "Meta Ads Data",
            "description": "Facebook and Instagram advertising performance data",
            "fields": [
                {
                    "name": "Reporting starts",
                    "type": "date",
                    "options": {
                        "dateFormat": {"name": "us", "format": "M/D/YYYY"}
                    }
                },
                {
                    "name": "Reporting ends",
                    "type": "date",
                    "options": {
                        "dateFormat": {"name": "us", "format": "M/D/YYYY"}
                    }
                },
                {
                    "name": "Ad name",
                    "type": "singleLineText"
                },
                {
                    "name": "Ad delivery",
                    "type": "singleLineText"
                },
                {
                    "name": "Ad Set Name",
                    "type": "singleLineText"
                },
                {
                    "name": "Bid",
                    "type": "currency",
                    "options": {
                        "precision": 2,
                        "symbol": "$"
                    }
                },
                {
                    "name": "Bid type",
                    "type": "singleLineText"
                },
                {
                    "name": "Ad set budget",
                    "type": "currency",
                    "options": {
                        "precision": 2,
                        "symbol": "$"
                    }
                },
                {
                    "name": "Ad set budget type",
                    "type": "singleLineText"
                },
                {
                    "name": "Last significant edit",
                    "type": "dateTime",
                    "options": {
                        "dateFormat": {"name": "us", "format": "M/D/YYYY"},
                        "timeFormat": {"name": "12hour", "format": "h:mma"},
                        "timeZone": "America/New_York"
                    }
                },
                {
                    "name": "Attribution setting",
                    "type": "singleLineText"
                },
                {
                    "name": "Results",
                    "type": "number",
                    "options": {"precision": 0}
                },
                {
                    "name": "Result indicator",
                    "type": "singleLineText"
                },
                {
                    "name": "Reach",
                    "type": "number",
                    "options": {"precision": 0}
                },
                {
                    "name": "Impressions",
                    "type": "number",
                    "options": {"precision": 0}
                },
                {
                    "name": "Cost per results",
                    "type": "currency",
                    "options": {
                        "precision": 2,
                        "symbol": "$"
                    }
                },
                {
                    "name": "Quality ranking",
                    "type": "singleLineText"
                },
                {
                    "name": "Engagement rate ranking",
                    "type": "singleLineText"
                },
                {
                    "name": "Conversion rate ranking",
                    "type": "singleLineText"
                },
                {
                    "name": "Amount spent (USD)",
                    "type": "currency",
                    "options": {
                        "precision": 2,
                        "symbol": "$"
                    }
                }
            ]
        }

    def get_workspaces(self):
        """Get list of available workspaces"""
        try:
            response = requests.get(
                f"{self.meta_url}/workspaces",
                headers=self.headers,
                timeout=10
            )

            if response.status_code == 200:
                data = response.json()
                workspaces = []

                for workspace in data.get('workspaces', []):
                    workspace_info = {
                        'id': workspace.get('id'),
                        'name': workspace.get('name'),
                        'permission_level': workspace.get('permissionLevel')
                    }
                    workspaces.append(workspace_info)

                logger.info(f"Found {len(workspaces)} workspaces")
                return True, workspaces
            elif response.status_code == 403:
                # API token doesn't have workspace permissions
                logger.info("Workspace access not available with current API token")
                return True, []  # Return empty list instead of error
            else:
                error_msg = f"Error getting workspaces: {response.status_code} - {response.text}"
                logger.warning(error_msg)
                return False, error_msg

        except requests.exceptions.RequestException as e:
            error_msg = f"Error getting workspaces: {str(e)}"
            logger.warning(error_msg)
            return False, error_msg

# Railway API Integration
class RailwayAPIManager:
    """Manages Railway API operations for automated deployment"""

    def __init__(self, api_token=None):
        self.api_token = api_token or os.getenv('RAILWAY_API_TOKEN')
        self.base_url = "https://backboard.railway.app/graphql"
        self.headers = {
            "Authorization": f"Bearer {self.api_token}",
            "Content-Type": "application/json"
        }

    def test_connection(self):
        """Test Railway API connection"""
        query = """
        query {
            me {
                id
                name
                email
            }
        }
        """

        try:
            response = requests.post(
                self.base_url,
                json={"query": query},
                headers=self.headers,
                timeout=10
            )

            if response.status_code == 200:
                data = response.json()
                if 'errors' not in data:
                    user_info = data.get('data', {}).get('me', {})
                    return True, f"Connected as {user_info.get('name', 'Unknown')} ({user_info.get('email', 'No email')})"
                else:
                    return False, f"API Error: {data['errors']}"
            else:
                return False, f"HTTP Error: {response.status_code}"

        except Exception as e:
            return False, f"Connection Error: {str(e)}"

    def list_projects(self):
        """List all Railway projects"""
        query = """
        query {
            projects {
                edges {
                    node {
                        id
                        name
                        description
                        createdAt
                        updatedAt
                    }
                }
            }
        }
        """

        try:
            response = requests.post(
                self.base_url,
                json={"query": query},
                headers=self.headers,
                timeout=15
            )

            if response.status_code == 200:
                data = response.json()
                if 'errors' not in data:
                    projects = []
                    edges = data.get('data', {}).get('projects', {}).get('edges', [])
                    for edge in edges:
                        projects.append(edge['node'])
                    return True, projects
                else:
                    return False, f"API Error: {data['errors']}"
            else:
                return False, f"HTTP Error: {response.status_code}"

        except Exception as e:
            return False, f"Error listing projects: {str(e)}"

    def create_project(self, name, description="Attribution Dashboard Client Instance"):
        """Create a new Railway project"""
        mutation = """
        mutation ProjectCreate($input: ProjectCreateInput!) {
            projectCreate(input: $input) {
                id
                name
                description
            }
        }
        """

        variables = {
            "input": {
                "name": name,
                "description": description
            }
        }

        try:
            response = requests.post(
                self.base_url,
                json={"query": mutation, "variables": variables},
                headers=self.headers,
                timeout=15
            )

            if response.status_code == 200:
                data = response.json()
                if 'errors' not in data:
                    project = data.get('data', {}).get('projectCreate', {})
                    return True, project
                else:
                    return False, f"API Error: {data['errors']}"
            else:
                return False, f"HTTP Error: {response.status_code}"

        except Exception as e:
            return False, f"Error creating project: {str(e)}"

# Client Instance Generator
class ClientInstanceGenerator:
    """Generates optimized client instances with only enabled features"""

    def __init__(self, base_path="."):
        self.base_path = Path(base_path)
        self.template_files = [
            "server.py", "script.js", "index.html", "styles.css",
            "config.py", "requirements.txt", "Dockerfile", "railway.json"
        ]

    def generate_client_instance(self, client_config: 'ClientConfig', output_dir: str):
        """Generate a complete client instance with optimized code"""
        output_path = Path(output_dir)
        output_path.mkdir(parents=True, exist_ok=True)

        results = {
            "success": True,
            "files_created": [],
            "files_modified": [],
            "errors": []
        }

        try:
            # 1. Copy and optimize server.py
            self._optimize_server_py(client_config, output_path, results)

            # 2. Copy and optimize script.js
            self._optimize_script_js(client_config, output_path, results)

            # 3. Copy and optimize index.html
            self._optimize_index_html(client_config, output_path, results)

            # 4. Copy and optimize styles.css
            self._optimize_styles_css(client_config, output_path, results)

            # 5. Generate optimized config.py
            self._generate_config_py(client_config, output_path, results)

            # 6. Copy deployment files
            self._copy_deployment_files(client_config, output_path, results)

            # 7. Generate client configuration file
            self._generate_client_config_file(client_config, output_path, results)

            # 8. Generate README for the client
            self._generate_client_readme(client_config, output_path, results)

        except Exception as e:
            results["success"] = False
            results["errors"].append(f"Generation failed: {str(e)}")

        return results

    def _optimize_server_py(self, config: 'ClientConfig', output_path: Path, results: dict):
        """Create optimized server.py with only enabled data sources"""
        source_file = self.base_path / "server.py"
        target_file = output_path / "server.py"

        if not source_file.exists():
            results["errors"].append("server.py template not found")
            return

        with open(source_file, 'r', encoding='utf-8') as f:
            content = f.read()

        # Add client configuration loading at the top
        config_injection = f'''
# Client-specific configuration (auto-generated)
CLIENT_CONFIG = {json.dumps(config.to_json_config(), indent=4)}

# Override default configuration with client-specific settings
def load_client_config():
    return CLIENT_CONFIG

'''

        # Insert after imports
        import_end = content.find('\napp = Flask(__name__)')
        if import_end != -1:
            content = content[:import_end] + config_injection + content[import_end:]

        # Optimize data source handling based on enabled sources
        enabled_sources = config.enabled_data_sources

        # Comment out disabled data source endpoints
        if 'pos' not in enabled_sources:
            content = self._comment_out_pos_code(content)
        if 'meta_ads' not in enabled_sources:
            content = self._comment_out_meta_ads_code(content)

        with open(target_file, 'w', encoding='utf-8') as f:
            f.write(content)

        results["files_created"].append(str(target_file))

    def _optimize_script_js(self, config: 'ClientConfig', output_path: Path, results: dict):
        """Create optimized script.js with only enabled features"""
        source_file = self.base_path / "script.js"
        target_file = output_path / "script.js"

        if not source_file.exists():
            results["errors"].append("script.js template not found")
            return

        with open(source_file, 'r', encoding='utf-8') as f:
            content = f.read()

        # Inject client configuration
        config_js = f'''
// Client Configuration (auto-generated)
const CLIENT_CONFIG = {json.dumps(config.to_json_config(), indent=2)};

// Override the ClientConfigManager to use embedded config
class EmbeddedClientConfigManager {{
    constructor() {{
        this.config = CLIENT_CONFIG;
        this.isLoaded = true;
    }}

    async loadConfig() {{
        return this.config;
    }}

    isDataSourceEnabled(source) {{
        return this.config.data_sources?.enabled_sources?.includes(source) || false;
    }}

    isTabEnabled(tab) {{
        return this.config.tab_configuration?.enabled_tabs?.includes(tab) || false;
    }}

    getEnabledDataSources() {{
        return this.config.data_sources?.enabled_sources || [];
    }}

    getEnabledTabs() {{
        return this.config.tab_configuration?.enabled_tabs || [];
    }}

    getTabLabel(tab) {{
        return this.config.tab_configuration?.tab_labels?.[tab] || tab;
    }}

    shouldHideDisabledFeatures() {{
        return this.config.ui_configuration?.hide_disabled_features !== false;
    }}

    getBusinessName() {{
        return this.config.client_info?.business_name || 'Attribution Dashboard';
    }}
}}

// Replace the global clientConfig with embedded version
const clientConfig = new EmbeddedClientConfigManager();

'''

        # Insert after the original ClientConfigManager
        manager_end = content.find('const clientConfig = new ClientConfigManager();')
        if manager_end != -1:
            # Replace the line
            content = content.replace(
                'const clientConfig = new ClientConfigManager();',
                config_js.strip()
            )

        with open(target_file, 'w', encoding='utf-8') as f:
            f.write(content)

        results["files_created"].append(str(target_file))

    def _optimize_index_html(self, config: 'ClientConfig', output_path: Path, results: dict):
        """Create optimized index.html with client branding"""
        source_file = self.base_path / "index.html"
        target_file = output_path / "index.html"

        if not source_file.exists():
            results["errors"].append("index.html template not found")
            return

        with open(source_file, 'r', encoding='utf-8') as f:
            content = f.read()

        # Update title and business name
        business_name = config.business_name or "Attribution Dashboard"
        content = content.replace(
            '<title>Attribution Dashboard</title>',
            f'<title>{business_name} - Attribution Dashboard</title>'
        )

        content = content.replace(
            '<span class="business-name">Attribution Dashboard</span>',
            f'<span class="business-name">{business_name}</span>'
        )

        with open(target_file, 'w', encoding='utf-8') as f:
            f.write(content)

        results["files_created"].append(str(target_file))

    def _optimize_styles_css(self, config: 'ClientConfig', output_path: Path, results: dict):
        """Copy styles.css with client customizations"""
        source_file = self.base_path / "styles.css"
        target_file = output_path / "styles.css"

        if not source_file.exists():
            results["errors"].append("styles.css template not found")
            return

        # For now, just copy the file - could add color customization later
        shutil.copy2(source_file, target_file)
        results["files_created"].append(str(target_file))

    def _generate_config_py(self, config: 'ClientConfig', output_path: Path, results: dict):
        """Generate optimized config.py with only enabled data sources"""
        target_file = output_path / "config.py"

        enabled_sources = config.enabled_data_sources

        config_content = f'''"""
Configuration file for {config.business_name or "Attribution Dashboard"}
Auto-generated client-specific configuration
"""

import os
from datetime import datetime

# Client Information
CLIENT_ID = "{config.client_id or 'auto-generated'}"
BUSINESS_NAME = "{config.business_name or 'Attribution Dashboard'}"
DASHBOARD_TITLE = "{config.dashboard_title or config.business_name or 'Attribution Dashboard'}"

# Environment Configuration
class Config:
    DEBUG = os.getenv('DEBUG', 'False').lower() == 'true'
    LOG_LEVEL = os.getenv('LOG_LEVEL', 'INFO')

    # Airtable Configuration
    AIRTABLE_API_KEY = os.getenv('AIRTABLE_API_KEY')
    AIRTABLE_BASE_ID = os.getenv('AIRTABLE_BASE_ID', '{config.airtable_base_id}')

    # Claude API Configuration
    CLAUDE_API_KEY = os.getenv('CLAUDE_API_KEY')

    # Performance Settings
    CACHE_TTL = {config.data_source_configs.get('ghl', type('obj', (object,), {{'cache_ttl': 300}})).cache_ttl}
    MAX_TOTAL_RECORDS = 10000
    MAX_RECORDS_PER_REQUEST = 1000

class DevelopmentConfig(Config):
    DEBUG = True
    LOG_LEVEL = 'DEBUG'

class ProductionConfig(Config):
    DEBUG = False
    LOG_LEVEL = 'INFO'
    MAX_TOTAL_RECORDS = 5000

# Data Source Configuration (only enabled sources)
ENABLED_DATA_SOURCES = {enabled_sources}

'''

        # Add table configurations only for enabled sources
        if 'ghl' in enabled_sources:
            ghl_config = config.data_source_configs.get('ghl')
            config_content += f'''
# GHL Configuration
GHL_TABLE_CONFIG = {{
    'id': '{ghl_config.table_id if ghl_config else "tblcdFVUC3zJrbmNf"}',
    'name': 'Fresh GHL',
    'date_field': '{ghl_config.date_field if ghl_config else "Date Created"}',
    'sort_direction': 'desc'
}}
'''

        if 'google_ads' in enabled_sources:
            gads_config = config.data_source_configs.get('google_ads')
            config_content += f'''
# Google Ads Configuration
GOOGLE_ADS_TABLE_CONFIG = {{
    'id': '{gads_config.table_id if gads_config else "tblRBXdh6L6zm9CZn"}',
    'name': 'Fresh Google Ads',
    'date_field': '{gads_config.date_field if gads_config else "Date"}',
    'sort_direction': 'desc'
}}
'''

        if 'pos' in enabled_sources:
            pos_config = config.data_source_configs.get('pos')
            config_content += f'''
# POS Configuration
POS_TABLE_CONFIG = {{
    'id': '{pos_config.table_id if pos_config else "tblHyyZHUsTdEb3BL"}',
    'name': 'Fresh POS',
    'date_field': '{pos_config.date_field if pos_config else "Created"}',
    'sort_direction': 'desc'
}}
'''

        config_content += '''
# Get configuration based on environment
def get_config():
    env = os.getenv('FLASK_ENV', 'production')
    if env == 'development':
        return DevelopmentConfig()
    return ProductionConfig()
'''

        with open(target_file, 'w', encoding='utf-8') as f:
            f.write(config_content)

        results["files_created"].append(str(target_file))

    def _copy_deployment_files(self, config: 'ClientConfig', output_path: Path, results: dict):
        """Copy deployment files (Dockerfile, railway.json, requirements.txt)"""
        deployment_files = ["Dockerfile", "railway.json", "requirements.txt"]

        for filename in deployment_files:
            source_file = self.base_path / filename
            target_file = output_path / filename

            if source_file.exists():
                shutil.copy2(source_file, target_file)
                results["files_created"].append(str(target_file))
            else:
                results["errors"].append(f"{filename} template not found")

    def _generate_client_config_file(self, config: 'ClientConfig', output_path: Path, results: dict):
        """Generate the client_config.json file"""
        target_file = output_path / "client_config.json"

        with open(target_file, 'w', encoding='utf-8') as f:
            json.dump(config.to_json_config(), f, indent=2)

        results["files_created"].append(str(target_file))

    def _generate_client_readme(self, config: 'ClientConfig', output_path: Path, results: dict):
        """Generate a README file for the client instance"""
        target_file = output_path / "README.md"

        enabled_sources = config.enabled_data_sources
        enabled_tabs = config.tab_config.enabled_tabs

        readme_content = f'''# {config.business_name or "Attribution Dashboard"}

This is a customized Attribution Dashboard instance generated specifically for {config.business_name or "this client"}.

## Configuration

- **Client ID**: {config.client_id or 'auto-generated'}
- **Business Name**: {config.business_name or 'Not specified'}
- **Generated**: {datetime.now().strftime("%Y-%m-%d %H:%M:%S")}

## Enabled Data Sources

{chr(10).join([f"- ✅ {source.upper()}" for source in enabled_sources])}

## Enabled Dashboard Tabs

{chr(10).join([f"- 📊 {config.tab_config.tab_labels.get(tab, tab.title())}" for tab in enabled_tabs])}

## Deployment

### Environment Variables Required

```bash
AIRTABLE_API_KEY=your_airtable_api_key_here
AIRTABLE_BASE_ID={config.airtable_base_id}
CLAUDE_API_KEY=your_claude_api_key_here
```

### Railway Deployment

1. Create a new Railway project
2. Connect this repository
3. Set the environment variables above
4. Deploy!

### Local Development

1. Install dependencies:
   ```bash
   pip install -r requirements.txt
   ```

2. Set environment variables in a `.env` file:
   ```bash
   AIRTABLE_API_KEY=your_key_here
   AIRTABLE_BASE_ID={config.airtable_base_id}
   CLAUDE_API_KEY=your_key_here
   FLASK_ENV=development
   ```

3. Run the application:
   ```bash
   python server.py
   ```

## Features

This instance includes only the features needed for {config.business_name or "this client"}:

- **Data Sources**: {", ".join(enabled_sources)}
- **Dashboard Tabs**: {", ".join([config.tab_config.tab_labels.get(tab, tab.title()) for tab in enabled_tabs])}
- **Performance**: Optimized for {config.cache_strategy} caching strategy
- **UI**: {"Simplified navigation" if config.simplified_navigation else "Full navigation"}

## Support

For support with this Attribution Dashboard instance, please contact your system administrator.
'''

        with open(target_file, 'w', encoding='utf-8') as f:
            f.write(readme_content)

        results["files_created"].append(str(target_file))

    def _comment_out_pos_code(self, content: str) -> str:
        """Comment out POS-related code sections"""
        # This is a simplified version - in a full implementation,
        # you'd want more sophisticated code analysis
        lines = content.split('\n')
        in_pos_section = False

        for i, line in enumerate(lines):
            if 'pos' in line.lower() and ('table' in line.lower() or 'config' in line.lower()):
                in_pos_section = True
            elif in_pos_section and line.strip() == '':
                in_pos_section = False

            if in_pos_section and not line.strip().startswith('#'):
                lines[i] = f"# DISABLED: {line}"

        return '\n'.join(lines)

    def _comment_out_meta_ads_code(self, content: str) -> str:
        """Comment out Meta Ads-related code sections"""
        lines = content.split('\n')
        in_meta_section = False

        for i, line in enumerate(lines):
            if 'meta' in line.lower() and ('table' in line.lower() or 'config' in line.lower()):
                in_meta_section = True
            elif in_meta_section and line.strip() == '':
                in_meta_section = False

            if in_meta_section and not line.strip().startswith('#'):
                lines[i] = f"# DISABLED: {line}"

        return '\n'.join(lines)

@dataclass
class DataSourceConfig:
    """Configuration for individual data sources"""
    table_id: str = ""
    cache_ttl: int = 300
    date_field: str = ""
    enabled: bool = True

@dataclass
class TabConfig:
    """Configuration for dashboard tabs"""
    enabled_tabs: List[str] = None
    tab_order: List[str] = None
    default_tab: str = "overview"
    tab_labels: Dict[str, str] = None

    def __post_init__(self):
        if self.enabled_tabs is None:
            self.enabled_tabs = ["overview", "ghl", "google_ads"]
        if self.tab_order is None:
            self.tab_order = self.enabled_tabs.copy()
        if self.tab_labels is None:
            self.tab_labels = {
                "overview": "Master Overview",
                "ghl": "GHL Analytics",
                "google_ads": "Google Ads",
                "pos": "POS Analytics",
                "meta_ads": "Meta Ads",
                "sales_report": "Sales Report",
                "lead_report": "Lead Report"
            }

@dataclass
class ClientConfig:
    """Enhanced client configuration data structure"""
    # Basic Information
    business_name: str = ""
    dashboard_title: str = ""
    logo_path: str = ""
    primary_color: str = "#e91e63"
    secondary_color: str = "#ff5722"
    font_family: str = "Inter"
    locations: List[str] = None
    enable_location_filtering: bool = True
    single_location_mode: bool = False
    custom_css: str = ""
    airtable_base_id: str = ""
    contact_email: str = ""

    # Data Source Configuration
    enabled_data_sources: List[str] = None
    disabled_data_sources: List[str] = None
    data_source_configs: Dict[str, DataSourceConfig] = None

    # Tab Configuration
    tab_config: TabConfig = None

    # UI Configuration
    hide_disabled_features: bool = True
    show_data_source_indicators: bool = False
    simplified_navigation: bool = False

    # Performance Settings
    lazy_load_tabs: bool = True
    preload_data: List[str] = None
    cache_strategy: str = "moderate"  # aggressive, moderate, minimal

    # Client Metadata
    client_id: str = ""
    created_date: str = ""
    last_updated: str = ""

    def __post_init__(self):
        if self.locations is None:
            self.locations = ["Daphne", "Mobile", "Foley"]

        if self.enabled_data_sources is None:
            self.enabled_data_sources = ["ghl", "google_ads"]

        if self.disabled_data_sources is None:
            self.disabled_data_sources = ["pos", "meta_ads", "meta_ads_summary", "meta_ads_simplified"]

        if self.data_source_configs is None:
            self.data_source_configs = {
                "ghl": DataSourceConfig(
                    table_id="tblcdFVUC3zJrbmNf",
                    date_field="Date Created",
                    enabled=True
                ),
                "google_ads": DataSourceConfig(
                    table_id="tblRBXdh6L6zm9CZn",
                    date_field="Date",
                    enabled=True
                ),
                "pos": DataSourceConfig(
                    table_id="tblHyyZHUsTdEb3BL",
                    date_field="Created",
                    enabled=False
                ),
                "meta_ads": DataSourceConfig(
                    table_id="tbl7mWcQBNA2TQAjc",
                    date_field="Reporting ends",
                    enabled=False
                )
            }

        if self.tab_config is None:
            self.tab_config = TabConfig()

        if self.preload_data is None:
            self.preload_data = ["ghl"]

    def is_data_source_enabled(self, source: str) -> bool:
        """Check if a data source is enabled"""
        return source in self.enabled_data_sources

    def is_tab_enabled(self, tab: str) -> bool:
        """Check if a tab is enabled"""
        return tab in self.tab_config.enabled_tabs

    def get_enabled_table_ids(self) -> List[str]:
        """Get list of table IDs for enabled data sources"""
        table_ids = []
        for source in self.enabled_data_sources:
            if source in self.data_source_configs:
                config = self.data_source_configs[source]
                if config.enabled and config.table_id:
                    table_ids.append(config.table_id)
        return table_ids

    def to_json_config(self) -> Dict[str, Any]:
        """Convert to JSON configuration format matching the schema"""
        from datetime import datetime

        return {
            "client_info": {
                "client_id": self.client_id or f"client_{datetime.now().strftime('%Y%m%d_%H%M%S')}",
                "business_name": self.business_name,
                "created_date": self.created_date or datetime.now().isoformat(),
                "last_updated": datetime.now().isoformat()
            },
            "data_sources": {
                "enabled_sources": self.enabled_data_sources,
                "disabled_sources": self.disabled_data_sources,
                "source_configs": {
                    source: {
                        "table_id": config.table_id,
                        "cache_ttl": config.cache_ttl,
                        "date_field": config.date_field
                    }
                    for source, config in self.data_source_configs.items()
                    if config.enabled
                }
            },
            "tab_configuration": {
                "enabled_tabs": self.tab_config.enabled_tabs,
                "tab_order": self.tab_config.tab_order,
                "default_tab": self.tab_config.default_tab,
                "tab_labels": self.tab_config.tab_labels
            },
            "ui_configuration": {
                "hide_disabled_features": self.hide_disabled_features,
                "show_data_source_indicators": self.show_data_source_indicators,
                "simplified_navigation": self.simplified_navigation
            },
            "performance_settings": {
                "lazy_load_tabs": self.lazy_load_tabs,
                "preload_data": self.preload_data,
                "cache_strategy": self.cache_strategy
            },
            "airtable_configuration": {
                "base_id": self.airtable_base_id,
                "api_key_env_var": "AIRTABLE_API_KEY"
            }
        }

@dataclass
class FileAnalysisResult:
    """Results from file analysis"""
    file_path: str
    exists: bool
    size: int
    location_references: List[str]
    customizable_elements: List[str]
    validation_errors: List[str]
    modification_suggestions: List[str]

class DashboardAnalyzer:
    """Analyzes the Attribution Dashboard codebase for customization opportunities"""
    
    def __init__(self):
        self.location_patterns = [
            r'\b(Daphne|Mobile|Foley)\b',
            r'location-filter',
            r'Location',
            r'locationData',
            r'locationBreakdown',
            r'Quick Fix - (Daphne|Mobile|Foley)'
        ]
        
        self.branding_patterns = [
            r'RepairLift',
            r'Attribution Dashboard',
            r'--primary:\s*#[a-fA-F0-9]{6}',
            r'--secondary:\s*#[a-fA-F0-9]{6}',
            r'logo-img',
            r'business_name'
        ]
        
        self.required_files = [
            'server.py',
            'index.html', 
            'script.js',
            'styles.css',
            'config.py',
            'requirements.txt'
        ]
    
    def analyze_directory(self, directory_path: str) -> Dict[str, FileAnalysisResult]:
        """Analyze the dashboard directory for customization opportunities"""
        results = {}
        
        for filename in self.required_files:
            file_path = os.path.join(directory_path, filename)
            results[filename] = self._analyze_file(file_path)
        
        return results
    
    def _analyze_file(self, file_path: str) -> FileAnalysisResult:
        """Analyze a single file for location dependencies and customization points"""
        result = FileAnalysisResult(
            file_path=file_path,
            exists=os.path.exists(file_path),
            size=0,
            location_references=[],
            customizable_elements=[],
            validation_errors=[],
            modification_suggestions=[]
        )
        
        if not result.exists:
            result.validation_errors.append(f"File not found: {file_path}")
            return result
        
        try:
            result.size = os.path.getsize(file_path)
            
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # Find location references
            for pattern in self.location_patterns:
                matches = re.findall(pattern, content, re.IGNORECASE)
                result.location_references.extend(matches)
            
            # Find branding elements
            for pattern in self.branding_patterns:
                matches = re.findall(pattern, content, re.IGNORECASE)
                result.customizable_elements.extend(matches)
            
            # File-specific analysis
            if file_path.endswith('.html'):
                result = self._analyze_html_file(content, result)
            elif file_path.endswith('.js'):
                result = self._analyze_js_file(content, result)
            elif file_path.endswith('.css'):
                result = self._analyze_css_file(content, result)
            elif file_path.endswith('.py'):
                result = self._analyze_python_file(content, result)
                
        except Exception as e:
            result.validation_errors.append(f"Error analyzing file: {str(e)}")
        
        return result
    
    def _analyze_html_file(self, content: str, result: FileAnalysisResult) -> FileAnalysisResult:
        """Analyze HTML file for specific customization opportunities"""
        # Check for location filter dropdown
        if 'location-filter' in content:
            result.modification_suggestions.append("Location filter dropdown found - can be hidden for single-location clients")
        
        # Check for location-specific cards
        if 'location-stat' in content:
            result.modification_suggestions.append("Location-specific stat cards found - can be customized or hidden")
        
        # Check for logo references
        if 'logo-img' in content:
            result.modification_suggestions.append("Logo image found - can be replaced with client logo")
        
        # Check for hardcoded business names
        if 'RepairLift' in content or 'Quick Fix' in content:
            result.modification_suggestions.append("Hardcoded business names found - should be replaced with client name")
        
        return result
    
    def _analyze_js_file(self, content: str, result: FileAnalysisResult) -> FileAnalysisResult:
        """Analyze JavaScript file for location dependencies"""
        # Count location-related functions
        location_functions = re.findall(r'function.*[Ll]ocation.*\(', content)
        if location_functions:
            result.modification_suggestions.append(f"Found {len(location_functions)} location-related functions")
        
        # Check for location filtering logic
        if 'currentLocationFilter' in content:
            result.modification_suggestions.append("Location filtering logic found - can be disabled for single-location clients")
        
        # Check for location charts
        location_charts = re.findall(r'location.*Chart', content, re.IGNORECASE)
        if location_charts:
            result.modification_suggestions.append(f"Found {len(set(location_charts))} location-based charts")
        
        return result
    
    def _analyze_css_file(self, content: str, result: FileAnalysisResult) -> FileAnalysisResult:
        """Analyze CSS file for styling customization opportunities"""
        # Check for color variables
        color_vars = re.findall(r'--\w+:\s*#[a-fA-F0-9]{6}', content)
        if color_vars:
            result.modification_suggestions.append(f"Found {len(color_vars)} color variables that can be customized")
        
        # Check for location-specific styles
        if 'location-' in content:
            result.modification_suggestions.append("Location-specific CSS classes found")
        
        return result
    
    def _analyze_python_file(self, content: str, result: FileAnalysisResult) -> FileAnalysisResult:
        """Analyze Python file for configuration opportunities"""
        # Check for configuration classes
        if 'class.*Config' in content:
            result.modification_suggestions.append("Configuration classes found - can be customized for client")
        
        # Check for hardcoded values
        if 'AIRTABLE_BASE_URL' in content:
            result.modification_suggestions.append("Airtable configuration found - needs client-specific base ID")
        
        return result

class DashboardCustomizer:
    """Handles the customization and generation of client-specific dashboards"""
    
    def __init__(self):
        self.analyzer = DashboardAnalyzer()
    
    def customize_dashboard(self, source_dir: str, output_dir: str, config: ClientConfig) -> bool:
        """Generate a customized dashboard for a specific client"""
        try:
            # Create output directory
            os.makedirs(output_dir, exist_ok=True)
            
            # Copy all files first
            self._copy_dashboard_files(source_dir, output_dir)
            
            # Apply customizations
            self._customize_html_files(output_dir, config)
            self._customize_css_files(output_dir, config)
            self._customize_js_files(output_dir, config)
            self._customize_python_files(output_dir, config)
            
            # Generate client-specific configuration
            self._generate_client_config(output_dir, config)
            
            logger.info(f"Successfully customized dashboard for {config.business_name}")
            return True
            
        except Exception as e:
            logger.error(f"Error customizing dashboard: {str(e)}")
            return False
    
    def _copy_dashboard_files(self, source_dir: str, output_dir: str):
        """Copy all dashboard files to output directory"""
        for item in os.listdir(source_dir):
            source_path = os.path.join(source_dir, item)
            dest_path = os.path.join(output_dir, item)
            
            if os.path.isfile(source_path):
                shutil.copy2(source_path, dest_path)
            elif os.path.isdir(source_path) and item not in ['.git', '__pycache__', 'logs']:
                shutil.copytree(source_path, dest_path, dirs_exist_ok=True)
    
    def _customize_html_files(self, output_dir: str, config: ClientConfig):
        """Customize HTML files with client-specific branding"""
        html_file = os.path.join(output_dir, 'index.html')
        if not os.path.exists(html_file):
            return
        
        with open(html_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Replace business name and title
        content = re.sub(r'RepairLift Attribution Logo', f'{config.business_name} Attribution Logo', content)
        content = re.sub(r'<title>.*?</title>', f'<title>{config.dashboard_title or config.business_name + " Attribution Dashboard"}</title>', content)

        # Add business name display to header (after logo)
        header_business_name = f'''<div class="business-name-display">
                <h2 class="business-name">{config.business_name}</h2>
                <p class="business-subtitle">Attribution Dashboard</p>
            </div>'''

        # Insert business name after logo div
        content = re.sub(
            r'(<div class="logo">.*?</div>)',
            r'\1' + header_business_name,
            content,
            flags=re.DOTALL
        )
        
        # Replace logo if provided
        if config.logo_path:
            content = re.sub(r'src="img/rl\.svg"', f'src="{config.logo_path}"', content)
        
        # Handle single-location mode - hide all filters and simplify header
        if config.single_location_mode:
            # Hide the entire filter container for single-location clients
            content = re.sub(
                r'<div class="filter-container">.*?</div>\s*</div>\s*</div>',
                '''<div class="single-location-header">
                    <div class="location-badge">{}</div>
                </div>
            </div>
        </div>'''.format(config.locations[0] if config.locations else "Main Location"),
                content,
                flags=re.DOTALL
            )

            # Hide location-specific cards in the dashboard
            content = re.sub(
                r'<div class="location-stat".*?</div>\s*</div>',
                '<!-- Location stats hidden for single-location client -->',
                content,
                flags=re.DOTALL
            )
        else:
            # For multi-location clients, just update the location options
            if config.locations:
                # Replace location options in the dropdown
                location_options = '\n'.join([
                    f'                            <option value="{loc}">{loc}</option>'
                    for loc in config.locations
                ])

                content = re.sub(
                    r'(<option value="all">All Locations</option>)\s*<option value="Daphne">Daphne</option>\s*<option value="Mobile">Mobile</option>\s*<option value="Foley">Foley</option>',
                    r'\1\n' + location_options,
                    content
                )
        
        # Replace hardcoded location names with client locations
        if config.locations:
            for i, location in enumerate(['Daphne', 'Mobile', 'Foley']):
                if i < len(config.locations):
                    content = content.replace(f'Quick Fix - {location}', f'{config.business_name} - {config.locations[i]}')
                    content = content.replace(f'>{location}<', f'>{config.locations[i]}<')
        
        with open(html_file, 'w', encoding='utf-8') as f:
            f.write(content)
    
    def _customize_css_files(self, output_dir: str, config: ClientConfig):
        """Customize CSS files with client-specific styling"""
        css_file = os.path.join(output_dir, 'styles.css')
        if not os.path.exists(css_file):
            return
        
        with open(css_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Update color variables
        content = re.sub(r'--primary:\s*#[a-fA-F0-9]{6}', f'--primary: {config.primary_color}', content)
        content = re.sub(r'--secondary:\s*#[a-fA-F0-9]{6}', f'--secondary: {config.secondary_color}', content)
        
        # Update font family
        if config.font_family != "Inter":
            content = re.sub(r'font-family:\s*[^;]+', f'font-family: "{config.font_family}", sans-serif', content)
        
        # Add custom CSS if provided
        if config.custom_css:
            content += f"\n\n/* Client-specific custom styles */\n{config.custom_css}\n"
        
        # Add business name display styling
        business_name_css = f"""
/* Business name display in header */
.business-name-display {{
    margin-left: 20px;
    display: flex;
    flex-direction: column;
    justify-content: center;
}}

.business-name {{
    margin: 0;
    font-size: 1.8rem;
    font-weight: 700;
    color: {config.primary_color};
    line-height: 1.2;
}}

.business-subtitle {{
    margin: 0;
    font-size: 0.9rem;
    color: #666;
    font-weight: 500;
}}

.header-content {{
    display: flex;
    align-items: center;
    justify-content: space-between;
}}

.header-content > div:first-child {{
    display: flex;
    align-items: center;
}}
"""
        content += business_name_css

        # Add single-location specific styles
        if config.single_location_mode:
            content += f"""
/* Single-location mode styles */
.single-location-header {{
    display: flex;
    align-items: center;
    gap: 15px;
}}

.location-badge {{
    background: {config.primary_color};
    color: white;
    padding: 8px 16px;
    border-radius: 20px;
    font-size: 0.9rem;
    font-weight: 600;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}}

/* Hide all filtering elements for single-location clients */
.filter-container,
.location-filter,
.location-breakdown,
.location-stat,
#date-filter,
#location-filter,
.custom-range-btn,
.filter-group {{
    display: none !important;
}}

/* Hide location-specific charts and elements */
.location-chart-container,
.location-comparison,
.multi-location-stats {{
    display: none !important;
}}
"""
        else:
            content += """
/* Multi-location mode - ensure filters are visible */
.filter-container {
    display: flex !important;
}
"""
        
        with open(css_file, 'w', encoding='utf-8') as f:
            f.write(content)
    
    def _customize_js_files(self, output_dir: str, config: ClientConfig):
        """Customize JavaScript files for client-specific functionality"""
        js_file = os.path.join(output_dir, 'script.js')
        if not os.path.exists(js_file):
            return
        
        with open(js_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Replace hardcoded location names
        if config.locations:
            for i, location in enumerate(['Daphne', 'Mobile', 'Foley']):
                if i < len(config.locations):
                    content = content.replace(f"'{location}'", f"'{config.locations[i]}'")
                    content = content.replace(f'"{location}"', f'"{config.locations[i]}"')
                    content = content.replace(f'Quick Fix - {location}', f'{config.business_name} - {config.locations[i]}')
        
        # Handle single-location mode in JavaScript
        if config.single_location_mode:
            single_location = config.locations[0] if config.locations else "Main"

            # Set default location filter and disable filtering
            content = re.sub(
                r"let currentLocationFilter = 'all';",
                f"let currentLocationFilter = '{single_location}';",
                content
            )

            # Add comprehensive single-location mode script
            single_location_script = f"""
// Single-location mode: disable all filtering and hide UI elements
document.addEventListener('DOMContentLoaded', function() {{
    // Hide all filter elements
    const filterContainer = document.querySelector('.filter-container');
    const locationFilter = document.getElementById('location-filter');
    const dateFilter = document.getElementById('date-filter');
    const customRangeBtn = document.querySelector('.custom-range-btn');
    const filterGroups = document.querySelectorAll('.filter-group');

    // Hide filter container entirely
    if (filterContainer) {{
        filterContainer.style.display = 'none';
    }}

    // Hide individual filter elements as backup
    [locationFilter, dateFilter, customRangeBtn].forEach(element => {{
        if (element) element.style.display = 'none';
    }});

    filterGroups.forEach(group => {{
        if (group) group.style.display = 'none';
    }});

    // Set fixed location for all data processing
    window.SINGLE_LOCATION_MODE = true;
    window.FIXED_LOCATION = '{single_location}';

    // Override location filtering functions
    window.filterByLocation = function(location) {{
        // In single-location mode, always use the fixed location
        return window.FIXED_LOCATION;
    }};

    // Hide location-specific charts and elements
    const locationCharts = document.querySelectorAll('.location-chart-container, .location-comparison, .multi-location-stats');
    locationCharts.forEach(chart => {{
        if (chart) chart.style.display = 'none';
    }});

    // Update any location displays to show the single location
    const locationDisplays = document.querySelectorAll('.location-display, .current-location');
    locationDisplays.forEach(display => {{
        if (display) display.textContent = '{single_location}';
    }});
}});

// Override any location change handlers
function handleLocationChange() {{
    // Do nothing in single-location mode
    return false;
}}
"""
            content += single_location_script
        
        with open(js_file, 'w', encoding='utf-8') as f:
            f.write(content)
    
    def _customize_python_files(self, output_dir: str, config: ClientConfig):
        """Customize Python configuration files"""
        config_file = os.path.join(output_dir, 'config.py')
        if not os.path.exists(config_file):
            return
        
        with open(config_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Update Airtable base ID if provided
        if config.airtable_base_id:
            content = re.sub(
                r"AIRTABLE_BASE_ID = '[^']*'",
                f"AIRTABLE_BASE_ID = '{config.airtable_base_id}'",
                content
            )
        
        # Add client-specific configuration
        client_config_section = f"""
# Client-specific configuration for {config.business_name}
CLIENT_NAME = '{config.business_name}'
CLIENT_LOCATIONS = {config.locations}
SINGLE_LOCATION_MODE = {config.single_location_mode}
ENABLE_LOCATION_FILTERING = {config.enable_location_filtering}
"""
        
        content += client_config_section
        
        with open(config_file, 'w', encoding='utf-8') as f:
            f.write(content)
    
    def _generate_client_config(self, output_dir: str, config: ClientConfig):
        """Generate a client-specific configuration file"""
        config_data = asdict(config)
        config_file = os.path.join(output_dir, 'client_config.json')
        
        with open(config_file, 'w', encoding='utf-8') as f:
            json.dump(config_data, f, indent=2)

class DashboardCustomizerGUI:
    """Main GUI application for the Dashboard Customizer"""
    
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("Attribution Dashboard Customizer v1.0")
        self.root.geometry("1200x800")
        self.root.minsize(1000, 600)
        
        # Initialize components
        self.analyzer = DashboardAnalyzer()
        self.customizer = DashboardCustomizer()
        self.client_config = ClientConfig()
        self.source_directory = ""
        self.analysis_results = {}
        
        # Setup GUI
        self.setup_styles()
        self.create_widgets()
        self.setup_layout()
        
        # Load default values
        self.load_default_config()
    
    def setup_styles(self):
        """Configure ttk styles"""
        style = ttk.Style()
        style.theme_use('clam')
        
        # Configure custom styles
        style.configure('Title.TLabel', font=('Arial', 16, 'bold'))
        style.configure('Heading.TLabel', font=('Arial', 12, 'bold'))
        style.configure('Success.TLabel', foreground='green')
        style.configure('Error.TLabel', foreground='red')
        style.configure('Warning.TLabel', foreground='orange')
    
    def create_widgets(self):
        """Create all GUI widgets"""
        # Main notebook for tabs
        self.notebook = ttk.Notebook(self.root)
        
        # Create tabs
        self.create_analysis_tab()
        self.create_customization_tab()
        self.create_data_source_tab()
        self.create_airtable_tab()
        self.create_deployment_tab()
        self.create_generation_tab()
        self.create_logs_tab()
    
    def create_analysis_tab(self):
        """Create the file analysis tab"""
        self.analysis_frame = ttk.Frame(self.notebook)
        self.notebook.add(self.analysis_frame, text="📁 File Analysis")
        
        # Source directory selection
        source_frame = ttk.LabelFrame(self.analysis_frame, text="Source Directory", padding=10)
        source_frame.pack(fill='x', padx=10, pady=5)
        
        ttk.Label(source_frame, text="Attribution Dashboard Source Directory:").pack(anchor='w')
        
        dir_frame = ttk.Frame(source_frame)
        dir_frame.pack(fill='x', pady=5)
        
        self.source_dir_var = tk.StringVar()
        self.source_dir_entry = ttk.Entry(dir_frame, textvariable=self.source_dir_var, width=80)
        self.source_dir_entry.pack(side='left', fill='x', expand=True)
        
        ttk.Button(dir_frame, text="Browse", command=self.browse_source_directory).pack(side='right', padx=(5, 0))
        
        # Analysis controls
        controls_frame = ttk.Frame(source_frame)
        controls_frame.pack(fill='x', pady=5)
        
        ttk.Button(controls_frame, text="🔍 Analyze Codebase", command=self.analyze_codebase).pack(side='left')
        ttk.Button(controls_frame, text="📋 Validate Structure", command=self.validate_structure).pack(side='left', padx=(5, 0))
        
        # Analysis results
        results_frame = ttk.LabelFrame(self.analysis_frame, text="Analysis Results", padding=10)
        results_frame.pack(fill='both', expand=True, padx=10, pady=5)
        
        # Create treeview for results
        columns = ('File', 'Status', 'Size', 'Location Refs', 'Customizable', 'Issues')
        self.analysis_tree = ttk.Treeview(results_frame, columns=columns, show='headings', height=10)
        
        for col in columns:
            self.analysis_tree.heading(col, text=col)
            self.analysis_tree.column(col, width=120)
        
        # Scrollbars for treeview
        tree_scroll_y = ttk.Scrollbar(results_frame, orient='vertical', command=self.analysis_tree.yview)
        tree_scroll_x = ttk.Scrollbar(results_frame, orient='horizontal', command=self.analysis_tree.xview)
        self.analysis_tree.configure(yscrollcommand=tree_scroll_y.set, xscrollcommand=tree_scroll_x.set)
        
        self.analysis_tree.pack(side='left', fill='both', expand=True)
        tree_scroll_y.pack(side='right', fill='y')
        tree_scroll_x.pack(side='bottom', fill='x')
        
        # Details panel
        details_frame = ttk.LabelFrame(self.analysis_frame, text="File Details", padding=10)
        details_frame.pack(fill='x', padx=10, pady=5)
        
        self.details_text = scrolledtext.ScrolledText(details_frame, height=8, wrap='word')
        self.details_text.pack(fill='both', expand=True)
        
        # Bind selection event
        self.analysis_tree.bind('<<TreeviewSelect>>', self.on_file_select)
    
    def create_customization_tab(self):
        """Create the client customization tab"""
        self.customization_frame = ttk.Frame(self.notebook)
        self.notebook.add(self.customization_frame, text="🎨 Client Customization")

        # Create scrollable frame with proper layout
        canvas = tk.Canvas(self.customization_frame, highlightthickness=0)
        scrollbar = ttk.Scrollbar(self.customization_frame, orient="vertical", command=canvas.yview)
        scrollable_frame = ttk.Frame(canvas)

        # Configure scrolling and canvas window
        def configure_scroll_region(event=None):
            canvas.configure(scrollregion=canvas.bbox("all"))

        def configure_canvas_window(event=None):
            # Make the scrollable frame fill the canvas width
            canvas_width = event.width if event else canvas.winfo_width()
            canvas.itemconfig(canvas_window, width=canvas_width)

        scrollable_frame.bind("<Configure>", configure_scroll_region)
        canvas.bind("<Configure>", configure_canvas_window)

        # Create window and store reference for width configuration
        canvas_window = canvas.create_window((0, 0), window=scrollable_frame, anchor="nw")
        canvas.configure(yscrollcommand=scrollbar.set)

        # Pack scrollbar and canvas properly
        scrollbar.pack(side="right", fill="y")
        canvas.pack(side="left", fill="both", expand=True)

        # Bind mousewheel to canvas and scrollable frame
        def _on_mousewheel(event):
            canvas.yview_scroll(int(-1*(event.delta/120)), "units")

        def bind_mousewheel(event):
            canvas.bind_all("<MouseWheel>", _on_mousewheel)

        def unbind_mousewheel(event):
            canvas.unbind_all("<MouseWheel>")

        canvas.bind("<Enter>", bind_mousewheel)
        canvas.bind("<Leave>", unbind_mousewheel)
        scrollable_frame.bind("<Enter>", bind_mousewheel)
        scrollable_frame.bind("<Leave>", unbind_mousewheel)
        
        # Basic Information
        basic_frame = ttk.LabelFrame(scrollable_frame, text="Basic Information", padding=10)
        basic_frame.pack(fill='x', padx=10, pady=5)
        
        # Business name
        ttk.Label(basic_frame, text="Business Name:").grid(row=0, column=0, sticky='w', pady=2)
        self.business_name_var = tk.StringVar()
        ttk.Entry(basic_frame, textvariable=self.business_name_var, width=40).grid(row=0, column=1, sticky='ew', padx=(5, 0), pady=2)
        
        # Dashboard title
        ttk.Label(basic_frame, text="Dashboard Title:").grid(row=1, column=0, sticky='w', pady=2)
        self.dashboard_title_var = tk.StringVar()
        ttk.Entry(basic_frame, textvariable=self.dashboard_title_var, width=40).grid(row=1, column=1, sticky='ew', padx=(5, 0), pady=2)
        
        # Contact email
        ttk.Label(basic_frame, text="Contact Email:").grid(row=2, column=0, sticky='w', pady=2)
        self.contact_email_var = tk.StringVar()
        ttk.Entry(basic_frame, textvariable=self.contact_email_var, width=40).grid(row=2, column=1, sticky='ew', padx=(5, 0), pady=2)
        
        basic_frame.columnconfigure(1, weight=1)
        
        # Branding
        branding_frame = ttk.LabelFrame(scrollable_frame, text="Branding & Styling", padding=10)
        branding_frame.pack(fill='x', padx=10, pady=5)
        
        # Logo path
        ttk.Label(branding_frame, text="Logo Path:").grid(row=0, column=0, sticky='w', pady=2)
        logo_frame = ttk.Frame(branding_frame)
        logo_frame.grid(row=0, column=1, sticky='ew', padx=(5, 0), pady=2)
        
        self.logo_path_var = tk.StringVar()
        ttk.Entry(logo_frame, textvariable=self.logo_path_var).pack(side='left', fill='x', expand=True)
        ttk.Button(logo_frame, text="Browse", command=self.browse_logo).pack(side='right', padx=(5, 0))
        
        # Colors
        ttk.Label(branding_frame, text="Primary Color:").grid(row=1, column=0, sticky='w', pady=2)
        self.primary_color_var = tk.StringVar(value="#e91e63")
        color_frame1 = ttk.Frame(branding_frame)
        color_frame1.grid(row=1, column=1, sticky='ew', padx=(5, 0), pady=2)
        ttk.Entry(color_frame1, textvariable=self.primary_color_var, width=10).pack(side='left')
        self.primary_color_preview = tk.Label(color_frame1, width=3, bg=self.primary_color_var.get())
        self.primary_color_preview.pack(side='left', padx=(5, 0))
        
        ttk.Label(branding_frame, text="Secondary Color:").grid(row=2, column=0, sticky='w', pady=2)
        self.secondary_color_var = tk.StringVar(value="#ff5722")
        color_frame2 = ttk.Frame(branding_frame)
        color_frame2.grid(row=2, column=1, sticky='ew', padx=(5, 0), pady=2)
        ttk.Entry(color_frame2, textvariable=self.secondary_color_var, width=10).pack(side='left')
        self.secondary_color_preview = tk.Label(color_frame2, width=3, bg=self.secondary_color_var.get())
        self.secondary_color_preview.pack(side='left', padx=(5, 0))
        
        # Font family
        ttk.Label(branding_frame, text="Font Family:").grid(row=3, column=0, sticky='w', pady=2)
        self.font_family_var = tk.StringVar(value="Inter")
        font_combo = ttk.Combobox(branding_frame, textvariable=self.font_family_var, 
                                 values=["Inter", "Arial", "Helvetica", "Roboto", "Open Sans", "Lato"])
        font_combo.grid(row=3, column=1, sticky='ew', padx=(5, 0), pady=2)
        
        branding_frame.columnconfigure(1, weight=1)
        
        # Location Settings
        location_frame = ttk.LabelFrame(scrollable_frame, text="Location Settings", padding=10)
        location_frame.pack(fill='x', padx=10, pady=5)
        
        # Single location mode
        self.single_location_var = tk.BooleanVar()
        ttk.Checkbutton(location_frame, text="Single Location Mode (Hide location filtering)", 
                       variable=self.single_location_var, command=self.on_single_location_change).pack(anchor='w')
        
        # Enable location filtering
        self.enable_location_filtering_var = tk.BooleanVar(value=True)
        ttk.Checkbutton(location_frame, text="Enable Location Filtering", 
                       variable=self.enable_location_filtering_var).pack(anchor='w')
        
        # Locations list
        ttk.Label(location_frame, text="Locations (one per line):").pack(anchor='w', pady=(10, 0))
        self.locations_text = scrolledtext.ScrolledText(location_frame, height=4, width=50)
        self.locations_text.pack(fill='x', pady=5)
        self.locations_text.insert('1.0', "Daphne\nMobile\nFoley")
        
        # Airtable Configuration
        airtable_frame = ttk.LabelFrame(scrollable_frame, text="Airtable Configuration", padding=10)
        airtable_frame.pack(fill='x', padx=10, pady=5)
        
        ttk.Label(airtable_frame, text="Airtable Base ID:").grid(row=0, column=0, sticky='w', pady=2)
        self.airtable_base_var = tk.StringVar()
        ttk.Entry(airtable_frame, textvariable=self.airtable_base_var, width=40).grid(row=0, column=1, sticky='ew', padx=(5, 0), pady=2)
        
        airtable_frame.columnconfigure(1, weight=1)
        
        # Custom CSS
        css_frame = ttk.LabelFrame(scrollable_frame, text="Custom CSS", padding=10)
        css_frame.pack(fill='both', expand=True, padx=10, pady=5)
        
        ttk.Label(css_frame, text="Additional CSS styles:").pack(anchor='w')
        self.custom_css_text = scrolledtext.ScrolledText(css_frame, height=6)
        self.custom_css_text.pack(fill='both', expand=True, pady=5)
        
        # Action buttons
        action_frame = ttk.Frame(scrollable_frame)
        action_frame.pack(fill='x', padx=10, pady=10)
        
        ttk.Button(action_frame, text="💾 Save Configuration", command=self.save_config).pack(side='left')
        ttk.Button(action_frame, text="📂 Load Configuration", command=self.load_config).pack(side='left', padx=(5, 0))
        ttk.Button(action_frame, text="🔄 Reset to Defaults", command=self.reset_config).pack(side='left', padx=(5, 0))
        ttk.Button(action_frame, text="🌐 Live Web Preview", command=self.open_web_preview).pack(side='right', padx=(5, 0))
        ttk.Button(action_frame, text="👁️ Preview Changes", command=self.preview_changes).pack(side='right')
        
        # Pack scrollable frame
        scrollbar.pack(side="right", fill="y")
        canvas.pack(side="left", fill="both", expand=True)

        # Bind mousewheel to canvas
        def _on_mousewheel(event):
            canvas.yview_scroll(int(-1*(event.delta/120)), "units")
        canvas.bind("<MouseWheel>", _on_mousewheel)

        # Bind color change events
        self.primary_color_var.trace('w', self.update_color_preview)
        self.secondary_color_var.trace('w', self.update_color_preview)

    def create_data_source_tab(self):
        """Create the data source configuration tab"""
        self.data_source_frame = ttk.Frame(self.notebook)
        self.notebook.add(self.data_source_frame, text="🔌 Data Sources")

        # Main container with scrolling
        canvas = tk.Canvas(self.data_source_frame, highlightthickness=0)
        scrollbar = ttk.Scrollbar(self.data_source_frame, orient="vertical", command=canvas.yview)
        scrollable_frame = ttk.Frame(canvas)

        # Configure scrolling and canvas window
        def configure_scroll_region(event=None):
            canvas.configure(scrollregion=canvas.bbox("all"))

        def configure_canvas_window(event=None):
            # Make the scrollable frame fill the canvas width
            canvas_width = event.width if event else canvas.winfo_width()
            canvas.itemconfig(canvas_window, width=canvas_width)

        scrollable_frame.bind("<Configure>", configure_scroll_region)
        canvas.bind("<Configure>", configure_canvas_window)

        # Create window and store reference for width configuration
        canvas_window = canvas.create_window((0, 0), window=scrollable_frame, anchor="nw")
        canvas.configure(yscrollcommand=scrollbar.set)

        # Pack scrollbar and canvas properly
        scrollbar.pack(side="right", fill="y")
        canvas.pack(side="left", fill="both", expand=True)

        # Bind mousewheel to canvas and scrollable frame
        def _on_mousewheel(event):
            canvas.yview_scroll(int(-1*(event.delta/120)), "units")

        def bind_mousewheel(event):
            canvas.bind_all("<MouseWheel>", _on_mousewheel)

        def unbind_mousewheel(event):
            canvas.unbind_all("<MouseWheel>")

        canvas.bind("<Enter>", bind_mousewheel)
        canvas.bind("<Leave>", unbind_mousewheel)
        scrollable_frame.bind("<Enter>", bind_mousewheel)
        scrollable_frame.bind("<Leave>", unbind_mousewheel)

        # Data Source Selection
        source_frame = ttk.LabelFrame(scrollable_frame, text="Data Source Selection", padding=10)
        source_frame.pack(fill='x', padx=10, pady=5)

        ttk.Label(source_frame, text="Select which data sources to enable for this client:",
                 style='Heading.TLabel').pack(anchor='w', pady=(0, 10))

        # Create checkboxes for each data source
        self.data_source_vars = {}
        data_sources = [
            ("ghl", "GHL (GoHighLevel)", "Lead generation and customer management data"),
            ("google_ads", "Google Ads", "Google advertising campaign performance"),
            ("pos", "POS (Point of Sale)", "Sales transaction and customer data"),
            ("meta_ads", "Meta Ads", "Facebook and Instagram advertising data"),
            ("meta_ads_summary", "Meta Ads Summary", "Summarized Meta advertising metrics"),
            ("meta_ads_simplified", "Meta Ads Simplified", "Simplified Meta advertising view")
        ]

        for source_id, source_name, description in data_sources:
            var = tk.BooleanVar()
            # Default enabled sources for new clients
            if source_id in ["ghl", "google_ads"]:
                var.set(True)

            self.data_source_vars[source_id] = var

            # Create frame for each data source
            ds_frame = ttk.Frame(source_frame)
            ds_frame.pack(fill='x', pady=2)

            # Checkbox and label
            cb = ttk.Checkbutton(ds_frame, text=source_name, variable=var,
                               command=self.on_data_source_change)
            cb.pack(side='left')

            # Description label
            desc_label = ttk.Label(ds_frame, text=f"- {description}",
                                 foreground='gray', font=('Arial', 9))
            desc_label.pack(side='left', padx=(10, 0))

        # Tab Configuration
        tab_frame = ttk.LabelFrame(scrollable_frame, text="Dashboard Tabs", padding=10)
        tab_frame.pack(fill='x', padx=10, pady=5)

        ttk.Label(tab_frame, text="Configure which tabs will be shown in the dashboard:",
                 style='Heading.TLabel').pack(anchor='w', pady=(0, 10))

        # Tab selection
        self.tab_vars = {}
        tabs = [
            ("overview", "Master Overview", "Combined view of all enabled data sources"),
            ("ghl", "GHL Analytics", "Detailed GHL performance metrics"),
            ("google_ads", "Google Ads", "Google advertising analytics"),
            ("pos", "POS Analytics", "Point of sale performance"),
            ("meta_ads", "Meta Ads", "Meta advertising analytics"),
            ("sales_report", "Sales Report", "Sales performance dashboard"),
            ("lead_report", "Lead Report", "Lead generation analytics")
        ]

        for tab_id, tab_name, description in tabs:
            var = tk.BooleanVar()
            # Default enabled tabs
            if tab_id in ["overview", "ghl", "google_ads"]:
                var.set(True)

            self.tab_vars[tab_id] = var

            # Create frame for each tab
            tab_item_frame = ttk.Frame(tab_frame)
            tab_item_frame.pack(fill='x', pady=2)

            # Checkbox and label
            cb = ttk.Checkbutton(tab_item_frame, text=tab_name, variable=var,
                               command=self.on_tab_change)
            cb.pack(side='left')

            # Description label
            desc_label = ttk.Label(tab_item_frame, text=f"- {description}",
                                 foreground='gray', font=('Arial', 9))
            desc_label.pack(side='left', padx=(10, 0))

        # Performance Settings
        perf_frame = ttk.LabelFrame(scrollable_frame, text="Performance Settings", padding=10)
        perf_frame.pack(fill='x', padx=10, pady=5)

        # Cache strategy
        ttk.Label(perf_frame, text="Cache Strategy:").grid(row=0, column=0, sticky='w', pady=2)
        self.cache_strategy_var = tk.StringVar(value="moderate")
        cache_combo = ttk.Combobox(perf_frame, textvariable=self.cache_strategy_var,
                                  values=["aggressive", "moderate", "minimal"], state="readonly")
        cache_combo.grid(row=0, column=1, sticky='ew', padx=(5, 0), pady=2)

        # Lazy loading
        self.lazy_load_var = tk.BooleanVar(value=True)
        ttk.Checkbutton(perf_frame, text="Enable lazy loading of tabs",
                       variable=self.lazy_load_var).grid(row=1, column=0, columnspan=2, sticky='w', pady=2)

        # Simplified navigation
        self.simplified_nav_var = tk.BooleanVar(value=False)
        ttk.Checkbutton(perf_frame, text="Use simplified navigation",
                       variable=self.simplified_nav_var).grid(row=2, column=0, columnspan=2, sticky='w', pady=2)

        perf_frame.columnconfigure(1, weight=1)

        # Preview Section
        preview_frame = ttk.LabelFrame(scrollable_frame, text="Configuration Preview", padding=10)
        preview_frame.pack(fill='both', expand=True, padx=10, pady=5)

        self.config_preview_text = scrolledtext.ScrolledText(preview_frame, height=12, wrap='word')
        self.config_preview_text.pack(fill='both', expand=True, pady=5)

        # Action buttons
        action_frame = ttk.Frame(scrollable_frame)
        action_frame.pack(fill='x', padx=10, pady=10)

        ttk.Button(action_frame, text="🔄 Update Preview",
                  command=self.update_config_preview).pack(side='left')
        ttk.Button(action_frame, text="📋 Copy Configuration",
                  command=self.copy_config_json).pack(side='left', padx=(5, 0))
        ttk.Button(action_frame, text="💾 Save Data Source Config",
                  command=self.save_data_source_config).pack(side='right')

        # Pack scrollable frame
        scrollbar.pack(side="right", fill="y")
        canvas.pack(side="left", fill="both", expand=True)

        # Bind mousewheel to canvas
        def _on_mousewheel(event):
            canvas.yview_scroll(int(-1*(event.delta/120)), "units")
        canvas.bind("<MouseWheel>", _on_mousewheel)

        # Initialize preview
        self.update_config_preview()

    def create_airtable_tab(self):
        """Create the Airtable management tab"""
        self.airtable_frame = ttk.Frame(self.notebook)
        self.notebook.add(self.airtable_frame, text="🗃️ Airtable Setup")

        # Main container with scrolling
        canvas = tk.Canvas(self.airtable_frame, highlightthickness=0)
        scrollbar = ttk.Scrollbar(self.airtable_frame, orient="vertical", command=canvas.yview)
        scrollable_frame = ttk.Frame(canvas)

        # Configure scrolling and canvas window
        def configure_scroll_region(event=None):
            canvas.configure(scrollregion=canvas.bbox("all"))

        def configure_canvas_window(event=None):
            # Make the scrollable frame fill the canvas width
            canvas_width = event.width if event else canvas.winfo_width()
            canvas.itemconfig(canvas_window, width=canvas_width)

        scrollable_frame.bind("<Configure>", configure_scroll_region)
        canvas.bind("<Configure>", configure_canvas_window)

        # Create window and store reference for width configuration
        canvas_window = canvas.create_window((0, 0), window=scrollable_frame, anchor="nw")
        canvas.configure(yscrollcommand=scrollbar.set)

        # Pack scrollbar and canvas properly
        scrollbar.pack(side="right", fill="y")
        canvas.pack(side="left", fill="both", expand=True)

        # Bind mousewheel to canvas and scrollable frame
        def _on_mousewheel(event):
            canvas.yview_scroll(int(-1*(event.delta/120)), "units")

        def bind_mousewheel(event):
            canvas.bind_all("<MouseWheel>", _on_mousewheel)

        def unbind_mousewheel(event):
            canvas.unbind_all("<MouseWheel>")

        canvas.bind("<Enter>", bind_mousewheel)
        canvas.bind("<Leave>", unbind_mousewheel)
        scrollable_frame.bind("<Enter>", bind_mousewheel)
        scrollable_frame.bind("<Leave>", unbind_mousewheel)

        # Initialize Airtable API Manager
        self.airtable_manager = AirtableAPIManager()

        # API Configuration Section
        api_frame = ttk.LabelFrame(scrollable_frame, text="🔑 Airtable API Configuration", padding=10)
        api_frame.pack(fill='x', padx=10, pady=5)

        # API Token input
        ttk.Label(api_frame, text="API Token:").grid(row=0, column=0, sticky='w', pady=2)
        self.airtable_token_var = tk.StringVar(value=os.getenv('AIRTABLE_API_KEY', ''))
        token_entry = ttk.Entry(api_frame, textvariable=self.airtable_token_var, width=50, show='*')
        token_entry.grid(row=0, column=1, sticky='ew', padx=(5, 0), pady=2)

        # Test connection button
        ttk.Button(api_frame, text="🔗 Test Connection",
                  command=self.test_airtable_connection).grid(row=0, column=2, padx=(5, 0), pady=2)

        # Connection status
        self.airtable_status_var = tk.StringVar(value="Not connected")
        status_label = ttk.Label(api_frame, textvariable=self.airtable_status_var)
        status_label.grid(row=1, column=0, columnspan=3, sticky='w', pady=2)

        api_frame.columnconfigure(1, weight=1)

        # Base Discovery Section
        discovery_frame = ttk.LabelFrame(scrollable_frame, text="🔍 Base Discovery", padding=10)
        discovery_frame.pack(fill='both', expand=True, padx=10, pady=5)

        # Refresh bases button
        refresh_frame = ttk.Frame(discovery_frame)
        refresh_frame.pack(fill='x', pady=(0, 10))

        self.refresh_button = ttk.Button(refresh_frame, text="🔄 Refresh Bases",
                                        command=self.refresh_airtable_bases)
        self.refresh_button.pack(side='left')
        ttk.Button(refresh_frame, text="📊 View Base Schema",
                  command=self.view_base_schema).pack(side='left', padx=(5, 0))

        # Selection mode toggle
        mode_frame = ttk.Frame(refresh_frame)
        mode_frame.pack(side='right')

        ttk.Label(mode_frame, text="Selection Mode:").pack(side='left', padx=(0, 5))
        self.selection_mode_var = tk.StringVar(value="single")
        mode_combo = ttk.Combobox(mode_frame, textvariable=self.selection_mode_var,
                                 values=["single", "multiple"], width=10, state="readonly")
        mode_combo.pack(side='left')
        mode_combo.bind('<<ComboboxSelected>>', self.on_selection_mode_change)

        # Bases list
        bases_list_frame = ttk.Frame(discovery_frame)
        bases_list_frame.pack(fill='both', expand=True)

        # Create treeview for bases with checkboxes for multi-select
        columns = ('Selected', 'Name', 'ID', 'Tables', 'Permission', 'Status')
        self.bases_tree = ttk.Treeview(bases_list_frame, columns=columns, show='headings', height=8)

        # Configure columns
        self.bases_tree.heading('Selected', text='✓')
        self.bases_tree.column('Selected', width=30, anchor='center')
        self.bases_tree.heading('Name', text='Name')
        self.bases_tree.column('Name', width=200)
        self.bases_tree.heading('ID', text='ID')
        self.bases_tree.column('ID', width=120)
        self.bases_tree.heading('Tables', text='Tables')
        self.bases_tree.column('Tables', width=60, anchor='center')
        self.bases_tree.heading('Permission', text='Permission')
        self.bases_tree.column('Permission', width=100)
        self.bases_tree.heading('Status', text='Status')
        self.bases_tree.column('Status', width=120)

        # Scrollbar for bases tree
        bases_scrollbar = ttk.Scrollbar(bases_list_frame, orient="vertical", command=self.bases_tree.yview)
        self.bases_tree.configure(yscrollcommand=bases_scrollbar.set)

        self.bases_tree.pack(side="left", fill="both", expand=True)
        bases_scrollbar.pack(side="right", fill="y")

        # Multi-selection controls
        multi_controls_frame = ttk.Frame(discovery_frame)
        multi_controls_frame.pack(fill='x', pady=(5, 0))

        ttk.Button(multi_controls_frame, text="☑️ Select All",
                  command=self.select_all_bases).pack(side='left')
        ttk.Button(multi_controls_frame, text="☐ Clear All",
                  command=self.clear_all_bases).pack(side='left', padx=(5, 0))

        # Selected bases counter
        self.selected_count_var = tk.StringVar(value="0 bases selected")
        ttk.Label(multi_controls_frame, textvariable=self.selected_count_var).pack(side='right')

        # Base selection info
        selection_frame = ttk.LabelFrame(scrollable_frame, text="📋 Selected Bases Information", padding=10)
        selection_frame.pack(fill='x', padx=10, pady=5)

        self.selected_base_info = scrolledtext.ScrolledText(selection_frame, height=6, wrap=tk.WORD)
        self.selected_base_info.pack(fill='both', expand=True)

        # Initialize selected bases tracking
        self.selected_bases = set()
        self.selected_bases_data = {}

        # Bind selection events
        self.bases_tree.bind('<<TreeviewSelect>>', self.on_base_select)
        self.bases_tree.bind('<Button-1>', self.on_base_click)
        self.bases_tree.bind('<Double-Button-1>', self.on_base_double_click)
        self.bases_tree.bind('<Button-3>', self.on_base_right_click)  # Right-click context menu

        # Client Configuration Section
        creation_frame = ttk.LabelFrame(scrollable_frame, text="🔧 Multi-Client Configuration", padding=10)
        creation_frame.pack(fill='x', padx=10, pady=5)

        # Instructions
        instructions = ttk.Label(creation_frame,
                               text="Select one or multiple bases above to check their structure and add missing Attribution Dashboard tables.",
                               foreground='blue')
        instructions.grid(row=0, column=0, columnspan=4, sticky='w', pady=(0, 10))

        # Action buttons
        button_frame = ttk.Frame(creation_frame)
        button_frame.grid(row=1, column=0, columnspan=4, pady=(10, 0), sticky='ew')

        ttk.Button(button_frame, text="🔍 Analyze Selected Bases",
                  command=self.analyze_selected_bases).pack(side='left')
        ttk.Button(button_frame, text="🏗️ Create Missing Tables",
                  command=self.create_missing_tables_multi).pack(side='left', padx=(5, 0))
        ttk.Button(button_frame, text="📋 Generate Configurations",
                  command=self.generate_multi_client_configs).pack(side='left', padx=(5, 0))
        ttk.Button(button_frame, text="❓ Token Help",
                  command=self.show_token_help).pack(side='right')

        creation_frame.columnconfigure(2, weight=1)

        # Analysis Results Section
        analysis_frame = ttk.LabelFrame(scrollable_frame, text="📊 Multi-Base Analysis Results", padding=10)
        analysis_frame.pack(fill='both', expand=True, padx=10, pady=5)

        # Create treeview for analysis results
        analysis_columns = ('Base', 'Missing Tables', 'Existing Tables', 'Status', 'Actions Needed')
        self.analysis_tree = ttk.Treeview(analysis_frame, columns=analysis_columns, show='headings', height=6)

        for col in analysis_columns:
            self.analysis_tree.heading(col, text=col)
            if col == 'Base':
                self.analysis_tree.column(col, width=200)
            elif col == 'Missing Tables':
                self.analysis_tree.column(col, width=150)
            elif col == 'Existing Tables':
                self.analysis_tree.column(col, width=150)
            elif col == 'Status':
                self.analysis_tree.column(col, width=100)
            else:
                self.analysis_tree.column(col, width=120)

        # Scrollbar for analysis tree
        analysis_scrollbar = ttk.Scrollbar(analysis_frame, orient="vertical", command=self.analysis_tree.yview)
        self.analysis_tree.configure(yscrollcommand=analysis_scrollbar.set)

        self.analysis_tree.pack(side="left", fill="both", expand=True)
        analysis_scrollbar.pack(side="right", fill="y")

        # Detailed analysis info
        details_frame = ttk.Frame(analysis_frame)
        details_frame.pack(fill='x', pady=(10, 0))

        self.analysis_details = scrolledtext.ScrolledText(details_frame, height=4, wrap=tk.WORD)
        self.analysis_details.pack(fill='both', expand=True)

        # Bind analysis tree selection
        self.analysis_tree.bind('<<TreeviewSelect>>', self.on_analysis_select)

        # Configuration Output Section
        output_frame = ttk.LabelFrame(scrollable_frame, text="⚙️ Generated Configuration", padding=10)
        output_frame.pack(fill='both', expand=True, padx=10, pady=5)

        # Configuration preview
        self.airtable_config_preview = scrolledtext.ScrolledText(output_frame, height=8, wrap=tk.WORD)
        self.airtable_config_preview.pack(fill='both', expand=True)

        # Action buttons
        action_frame = ttk.Frame(output_frame)
        action_frame.pack(fill='x', pady=(10, 0))

        ttk.Button(action_frame, text="📋 Copy Configuration",
                  command=self.copy_airtable_config).pack(side='left')
        ttk.Button(action_frame, text="💾 Save to Client Config",
                  command=self.save_airtable_config).pack(side='left', padx=(10, 0))
        ttk.Button(action_frame, text="🔄 Auto-Configure",
                  command=self.auto_configure_client).pack(side='right')

    def create_deployment_tab(self):
        """Create the Railway deployment management tab"""
        self.deployment_frame = ttk.Frame(self.notebook)
        self.notebook.add(self.deployment_frame, text="🚀 Deployment")

        # Main container with scrolling
        canvas = tk.Canvas(self.deployment_frame, highlightthickness=0)
        scrollbar = ttk.Scrollbar(self.deployment_frame, orient="vertical", command=canvas.yview)
        scrollable_frame = ttk.Frame(canvas)

        # Configure scrolling and canvas window
        def configure_scroll_region(event=None):
            canvas.configure(scrollregion=canvas.bbox("all"))

        def configure_canvas_window(event=None):
            # Make the scrollable frame fill the canvas width
            canvas_width = event.width if event else canvas.winfo_width()
            canvas.itemconfig(canvas_window, width=canvas_width)

        scrollable_frame.bind("<Configure>", configure_scroll_region)
        canvas.bind("<Configure>", configure_canvas_window)

        # Create window and store reference for width configuration
        canvas_window = canvas.create_window((0, 0), window=scrollable_frame, anchor="nw")
        canvas.configure(yscrollcommand=scrollbar.set)

        # Railway API Configuration
        api_frame = ttk.LabelFrame(scrollable_frame, text="Railway API Configuration", padding=10)
        api_frame.pack(fill='x', padx=10, pady=5)

        # API Token
        ttk.Label(api_frame, text="Railway API Token:").grid(row=0, column=0, sticky='w', pady=2)
        self.railway_token_var = tk.StringVar(value=os.getenv('RAILWAY_API_TOKEN', ''))
        token_entry = ttk.Entry(api_frame, textvariable=self.railway_token_var, show='*', width=50)
        token_entry.grid(row=0, column=1, sticky='ew', padx=(5, 0), pady=2)

        # Test connection button
        ttk.Button(api_frame, text="🔗 Test Connection",
                  command=self.test_railway_connection).grid(row=0, column=2, padx=(5, 0), pady=2)

        # Connection status
        self.connection_status_var = tk.StringVar(value="Not tested")
        status_label = ttk.Label(api_frame, textvariable=self.connection_status_var, foreground='gray')
        status_label.grid(row=1, column=0, columnspan=3, sticky='w', pady=2)

        api_frame.columnconfigure(1, weight=1)

        # Project Management
        project_frame = ttk.LabelFrame(scrollable_frame, text="Project Management", padding=10)
        project_frame.pack(fill='x', padx=10, pady=5)

        # Project list
        ttk.Label(project_frame, text="Existing Projects:").pack(anchor='w', pady=(0, 5))

        # Project listbox with scrollbar
        project_list_frame = ttk.Frame(project_frame)
        project_list_frame.pack(fill='both', expand=True, pady=5)

        self.project_listbox = tk.Listbox(project_list_frame, height=6)
        project_scrollbar = ttk.Scrollbar(project_list_frame, orient="vertical", command=self.project_listbox.yview)
        self.project_listbox.configure(yscrollcommand=project_scrollbar.set)

        self.project_listbox.pack(side="left", fill="both", expand=True)
        project_scrollbar.pack(side="right", fill="y")

        # Project management buttons
        project_buttons_frame = ttk.Frame(project_frame)
        project_buttons_frame.pack(fill='x', pady=5)

        ttk.Button(project_buttons_frame, text="🔄 Refresh Projects",
                  command=self.refresh_railway_projects).pack(side='left')
        ttk.Button(project_buttons_frame, text="➕ Create New Project",
                  command=self.create_railway_project).pack(side='left', padx=(5, 0))

        # New project configuration
        new_project_frame = ttk.LabelFrame(scrollable_frame, text="New Project Configuration", padding=10)
        new_project_frame.pack(fill='x', padx=10, pady=5)

        # Project name
        ttk.Label(new_project_frame, text="Project Name:").grid(row=0, column=0, sticky='w', pady=2)
        self.project_name_var = tk.StringVar()
        project_name_entry = ttk.Entry(new_project_frame, textvariable=self.project_name_var, width=40)
        project_name_entry.grid(row=0, column=1, sticky='ew', padx=(5, 0), pady=2)

        # Auto-generate project name button
        ttk.Button(new_project_frame, text="🎲 Auto-Generate",
                  command=self.auto_generate_project_name).grid(row=0, column=2, padx=(5, 0), pady=2)

        # Project description
        ttk.Label(new_project_frame, text="Description:").grid(row=1, column=0, sticky='w', pady=2)
        self.project_desc_var = tk.StringVar(value="Attribution Dashboard Client Instance")
        desc_entry = ttk.Entry(new_project_frame, textvariable=self.project_desc_var, width=40)
        desc_entry.grid(row=1, column=1, columnspan=2, sticky='ew', padx=(5, 0), pady=2)

        new_project_frame.columnconfigure(1, weight=1)

        # Deployment Status
        status_frame = ttk.LabelFrame(scrollable_frame, text="Deployment Status", padding=10)
        status_frame.pack(fill='x', padx=10, pady=5)

        self.deployment_status_text = scrolledtext.ScrolledText(status_frame, height=8, wrap='word')
        self.deployment_status_text.pack(fill='both', expand=True, pady=5)

        # Action buttons
        action_frame = ttk.Frame(scrollable_frame)
        action_frame.pack(fill='x', padx=10, pady=10)

        ttk.Button(action_frame, text="🚀 Deploy to Railway",
                  command=self.deploy_to_railway).pack(side='left')
        ttk.Button(action_frame, text="📊 Monitor Deployment",
                  command=self.monitor_deployment).pack(side='left', padx=(5, 0))
        ttk.Button(action_frame, text="🔄 Update Environment Variables",
                  command=self.update_env_vars).pack(side='right')

        # Pack scrollable frame
        scrollbar.pack(side="right", fill="y")
        canvas.pack(side="left", fill="both", expand=True)

        # Bind mousewheel to canvas and scrollable frame
        def _on_mousewheel(event):
            canvas.yview_scroll(int(-1*(event.delta/120)), "units")

        def bind_mousewheel(event):
            canvas.bind_all("<MouseWheel>", _on_mousewheel)

        def unbind_mousewheel(event):
            canvas.unbind_all("<MouseWheel>")

        canvas.bind("<Enter>", bind_mousewheel)
        canvas.bind("<Leave>", unbind_mousewheel)
        scrollable_frame.bind("<Enter>", bind_mousewheel)
        scrollable_frame.bind("<Leave>", unbind_mousewheel)

        # Initialize Railway API manager
        self.railway_api = None

        # Auto-load projects if token is available
        if self.railway_token_var.get():
            self.root.after(1000, self.test_railway_connection)

    def get_client_config(self):
        """Get current client configuration from GUI"""
        locations = [loc.strip() for loc in self.locations_text.get('1.0', tk.END).strip().split('\n') if loc.strip()]

        # Get enabled data sources
        enabled_sources = [source for source, var in self.data_source_vars.items() if var.get()]
        disabled_sources = [source for source, var in self.data_source_vars.items() if not var.get()]

        # Get enabled tabs
        enabled_tabs = [tab for tab, var in self.tab_vars.items() if var.get()]

        # Create tab config
        tab_config = TabConfig(
            enabled_tabs=enabled_tabs,
            tab_order=enabled_tabs.copy(),
            default_tab=enabled_tabs[0] if enabled_tabs else "overview"
        )

        # ✅ CRITICAL FIX: Integrate Airtable configuration with table IDs
        data_source_configs = {}
        airtable_base_id = self.airtable_base_var.get()

        # Use generated_config from Airtable tab if available
        if hasattr(self, 'generated_config') and self.generated_config:
            airtable_config = self.generated_config.get('airtable_configuration', {})
            source_configs = self.generated_config.get('data_sources', {}).get('source_configs', {})

            # Override base ID from generated config
            if airtable_config.get('base_id'):
                airtable_base_id = airtable_config['base_id']

            # Create DataSourceConfig objects with actual table IDs
            for source in enabled_sources:
                if source in source_configs:
                    config_data = source_configs[source]
                    data_source_configs[source] = DataSourceConfig(
                        table_id=config_data.get('table_id', ''),
                        date_field=config_data.get('date_field', 'Date Created'),
                        cache_ttl=config_data.get('cache_ttl', 300),
                        enabled=True
                    )
                else:
                    # Fallback to default configs
                    data_source_configs[source] = DataSourceConfig(
                        table_id='',  # Will use defaults in generation
                        date_field='Date Created',
                        cache_ttl=300,
                        enabled=True
                    )
        else:
            # Fallback: Create default configs for enabled sources
            for source in enabled_sources:
                data_source_configs[source] = DataSourceConfig(
                    table_id='',  # Will use defaults in generation
                    date_field='Date Created',
                    cache_ttl=300,
                    enabled=True
                )

        return ClientConfig(
            business_name=self.business_name_var.get(),
            dashboard_title=self.dashboard_title_var.get(),
            logo_path=self.logo_path_var.get(),
            primary_color=self.primary_color_var.get(),
            secondary_color=self.secondary_color_var.get(),
            font_family=self.font_family_var.get(),
            locations=locations,
            enable_location_filtering=self.enable_location_filtering_var.get(),
            single_location_mode=self.single_location_var.get(),
            custom_css=self.custom_css_text.get('1.0', tk.END).strip(),
            airtable_base_id=airtable_base_id,  # ✅ Use integrated base ID
            contact_email=self.contact_email_var.get(),
            # New fields
            enabled_data_sources=enabled_sources,
            disabled_data_sources=disabled_sources,
            data_source_configs=data_source_configs,  # ✅ Include actual table IDs
            tab_config=tab_config,
            simplified_navigation=self.simplified_nav_var.get() if hasattr(self, 'simplified_nav_var') else False,
            lazy_load_tabs=self.lazy_load_var.get() if hasattr(self, 'lazy_load_var') else True,
            cache_strategy=self.cache_strategy_var.get() if hasattr(self, 'cache_strategy_var') else "moderate"
        )
       
    def set_client_config(self, config: ClientConfig):
        """Set GUI values from client configuration"""
        self.business_name_var.set(config.business_name)
        self.dashboard_title_var.set(config.dashboard_title)
        self.logo_path_var.set(config.logo_path)
        self.primary_color_var.set(config.primary_color)
        self.secondary_color_var.set(config.secondary_color)
        self.font_family_var.set(config.font_family)
        self.enable_location_filtering_var.set(config.enable_location_filtering)
        self.single_location_var.set(config.single_location_mode)
        self.airtable_base_var.set(config.airtable_base_id)
        self.contact_email_var.set(config.contact_email)
        
        # Set locations
        self.locations_text.delete('1.0', tk.END)
        if config.locations:
            self.locations_text.insert('1.0', '\n'.join(config.locations))
        
        # Set custom CSS
        self.custom_css_text.delete('1.0', tk.END)
        if config.custom_css:
            self.custom_css_text.insert('1.0', config.custom_css)
        
        self.update_color_preview()

    def on_data_source_change(self):
        """Handle data source selection changes"""
        self.update_config_preview()

        # Auto-update tab selection based on data sources
        enabled_sources = [source for source, var in self.data_source_vars.items() if var.get()]

        # Auto-enable corresponding tabs
        for source in enabled_sources:
            if source in self.tab_vars:
                self.tab_vars[source].set(True)

        # Auto-disable tabs for disabled sources
        disabled_sources = [source for source, var in self.data_source_vars.items() if not var.get()]
        for source in disabled_sources:
            if source in self.tab_vars:
                self.tab_vars[source].set(False)

        # Always keep overview enabled if any sources are enabled
        if enabled_sources and 'overview' in self.tab_vars:
            self.tab_vars['overview'].set(True)

    def on_tab_change(self):
        """Handle tab selection changes"""
        self.update_config_preview()

    def update_config_preview(self):
        """Update the configuration preview text"""
        if not hasattr(self, 'config_preview_text'):
            return

        try:
            config = self.get_client_config()
            json_config = config.to_json_config()

            preview_text = "CONFIGURATION PREVIEW\n"
            preview_text += "=" * 50 + "\n\n"

            preview_text += f"Business: {config.business_name}\n"
            preview_text += f"Client ID: {json_config['client_info']['client_id']}\n\n"

            preview_text += "ENABLED DATA SOURCES:\n"
            for source in config.enabled_data_sources:
                preview_text += f"  ✓ {source.upper()}\n"

            preview_text += "\nDISABLED DATA SOURCES:\n"
            for source in config.disabled_data_sources:
                preview_text += f"  ✗ {source.upper()}\n"

            preview_text += "\nENABLED TABS:\n"
            for tab in config.tab_config.enabled_tabs:
                label = config.tab_config.tab_labels.get(tab, tab.title())
                preview_text += f"  📊 {label}\n"

            preview_text += f"\nPERFORMANCE SETTINGS:\n"
            preview_text += f"  Cache Strategy: {config.cache_strategy}\n"
            preview_text += f"  Lazy Loading: {'Yes' if config.lazy_load_tabs else 'No'}\n"
            preview_text += f"  Simplified Navigation: {'Yes' if config.simplified_navigation else 'No'}\n"

            preview_text += f"\nAIRTABLE CONFIGURATION:\n"
            preview_text += f"  Base ID: {config.airtable_base_id}\n"

            enabled_tables = config.get_enabled_table_ids()
            preview_text += f"  Enabled Tables: {len(enabled_tables)}\n"
            for table_id in enabled_tables:
                preview_text += f"    - {table_id}\n"

            preview_text += "\n" + "=" * 50 + "\n"
            preview_text += "JSON CONFIGURATION:\n"
            preview_text += "=" * 50 + "\n"
            preview_text += json.dumps(json_config, indent=2)

            self.config_preview_text.delete('1.0', tk.END)
            self.config_preview_text.insert('1.0', preview_text)

        except Exception as e:
            error_text = f"Error generating preview: {str(e)}"
            self.config_preview_text.delete('1.0', tk.END)
            self.config_preview_text.insert('1.0', error_text)

    def copy_config_json(self):
        """Copy the JSON configuration to clipboard"""
        try:
            config = self.get_client_config()
            json_config = config.to_json_config()
            json_text = json.dumps(json_config, indent=2)

            self.root.clipboard_clear()
            self.root.clipboard_append(json_text)
            messagebox.showinfo("Copied", "Configuration JSON copied to clipboard!")

        except Exception as e:
            messagebox.showerror("Error", f"Failed to copy configuration: {str(e)}")

    def save_data_source_config(self):
        """Save data source configuration to file"""
        try:
            config = self.get_client_config()
            json_config = config.to_json_config()

            file_path = filedialog.asksaveasfilename(
                title="Save Data Source Configuration",
                defaultextension=".json",
                filetypes=[("JSON files", "*.json"), ("All files", "*.*")],
                initialname=f"{config.business_name.replace(' ', '_')}_datasource_config.json" if config.business_name else "datasource_config.json"
            )

            if file_path:
                with open(file_path, 'w', encoding='utf-8') as f:
                    json.dump(json_config, f, indent=2)

                messagebox.showinfo("Success", f"Data source configuration saved to {file_path}")
                logger.info(f"Data source configuration saved to {file_path}")

        except Exception as e:
            error_msg = f"Error saving data source configuration: {str(e)}"
            messagebox.showerror("Save Error", error_msg)
            logger.error(error_msg)

    # Railway Deployment Methods
    def test_railway_connection(self):
        """Test Railway API connection"""
        try:
            token = self.railway_token_var.get().strip()
            if not token:
                self.connection_status_var.set("❌ No API token provided")
                return

            self.railway_api = RailwayAPIManager(token)
            success, message = self.railway_api.test_connection()

            if success:
                self.connection_status_var.set(f"✅ {message}")
                # Auto-refresh projects on successful connection
                self.refresh_railway_projects()
            else:
                self.connection_status_var.set(f"❌ {message}")

        except Exception as e:
            self.connection_status_var.set(f"❌ Error: {str(e)}")
            logger.error(f"Railway connection test failed: {str(e)}")

    def refresh_railway_projects(self):
        """Refresh the list of Railway projects"""
        if not self.railway_api:
            messagebox.showwarning("Warning", "Please test Railway connection first")
            return

        try:
            success, projects = self.railway_api.list_projects()

            if success:
                # Clear existing items
                self.project_listbox.delete(0, tk.END)

                # Add projects to listbox
                for project in projects:
                    display_text = f"{project['name']} (ID: {project['id'][:8]}...)"
                    self.project_listbox.insert(tk.END, display_text)

                self.update_deployment_status(f"✅ Loaded {len(projects)} projects from Railway")

            else:
                messagebox.showerror("Error", f"Failed to load projects: {projects}")

        except Exception as e:
            error_msg = f"Error refreshing projects: {str(e)}"
            messagebox.showerror("Error", error_msg)
            logger.error(error_msg)

    def auto_generate_project_name(self):
        """Auto-generate a project name based on business name"""
        business_name = self.business_name_var.get().strip()
        if business_name:
            # Convert to Railway-friendly name
            project_name = business_name.lower().replace(' ', '-').replace('_', '-')
            project_name = re.sub(r'[^a-z0-9-]', '', project_name)
            project_name = f"{project_name}-attribution-dashboard"
            self.project_name_var.set(project_name)
        else:
            # Generate timestamp-based name
            timestamp = datetime.now().strftime("%Y%m%d-%H%M%S")
            self.project_name_var.set(f"attribution-dashboard-{timestamp}")

    def create_railway_project(self):
        """Create a new Railway project"""
        if not self.railway_api:
            messagebox.showwarning("Warning", "Please test Railway connection first")
            return

        project_name = self.project_name_var.get().strip()
        if not project_name:
            messagebox.showwarning("Warning", "Please enter a project name")
            return

        project_desc = self.project_desc_var.get().strip()

        try:
            self.update_deployment_status(f"🚀 Creating Railway project: {project_name}")

            success, result = self.railway_api.create_project(project_name, project_desc)

            if success:
                self.update_deployment_status(f"✅ Project created successfully!")
                self.update_deployment_status(f"   Project ID: {result['id']}")
                self.update_deployment_status(f"   Project Name: {result['name']}")

                # Refresh project list
                self.refresh_railway_projects()

                messagebox.showinfo("Success", f"Railway project '{project_name}' created successfully!")

            else:
                error_msg = f"Failed to create project: {result}"
                self.update_deployment_status(f"❌ {error_msg}")
                messagebox.showerror("Error", error_msg)

        except Exception as e:
            error_msg = f"Error creating Railway project: {str(e)}"
            self.update_deployment_status(f"❌ {error_msg}")
            messagebox.showerror("Error", error_msg)
            logger.error(error_msg)

    def deploy_to_railway(self):
        """Deploy the current configuration to Railway"""
        # This is a placeholder for the full deployment process
        self.update_deployment_status("🚀 Starting deployment process...")
        self.update_deployment_status("📋 Generating client configuration...")

        try:
            config = self.get_client_config()
            json_config = config.to_json_config()

            self.update_deployment_status("✅ Configuration generated successfully")
            self.update_deployment_status("🔧 Next steps:")
            self.update_deployment_status("   1. Create Railway project (if not exists)")
            self.update_deployment_status("   2. Set up environment variables")
            self.update_deployment_status("   3. Deploy application code")
            self.update_deployment_status("   4. Configure custom domain (optional)")

            # For now, just show the configuration
            messagebox.showinfo("Deployment Ready",
                              "Configuration is ready for deployment. Full automation coming soon!")

        except Exception as e:
            error_msg = f"Error preparing deployment: {str(e)}"
            self.update_deployment_status(f"❌ {error_msg}")
            messagebox.showerror("Error", error_msg)

    def monitor_deployment(self):
        """Monitor deployment status"""
        self.update_deployment_status("📊 Monitoring deployment status...")
        # Placeholder for deployment monitoring
        messagebox.showinfo("Monitor", "Deployment monitoring feature coming soon!")

    def update_env_vars(self):
        """Update environment variables for Railway project"""
        self.update_deployment_status("🔄 Updating environment variables...")
        # Placeholder for environment variable management
        messagebox.showinfo("Environment Variables", "Environment variable management coming soon!")

    def update_deployment_status(self, message):
        """Update the deployment status text area"""
        if hasattr(self, 'deployment_status_text'):
            timestamp = datetime.now().strftime("%H:%M:%S")
            self.deployment_status_text.insert(tk.END, f"[{timestamp}] {message}\n")
            self.deployment_status_text.see(tk.END)
            self.root.update_idletasks()

    def save_config(self):
        """Save current configuration to file"""
        config = self.get_client_config()
        
        file_path = filedialog.asksaveasfilename(
            title="Save Client Configuration",
            defaultextension=".json",
            filetypes=[("JSON files", "*.json"), ("All files", "*.*")]
        )
        
        if file_path:
            try:
                with open(file_path, 'w', encoding='utf-8') as f:
                    json.dump(asdict(config), f, indent=2)
                
                messagebox.showinfo("Success", f"Configuration saved to {file_path}")
                self.status_var.set(f"Configuration saved: {file_path}")
                logger.info(f"Configuration saved to {file_path}")
                
            except Exception as e:
                error_msg = f"Error saving configuration: {str(e)}"
                messagebox.showerror("Save Error", error_msg)
                logger.error(error_msg)
    
    def load_config(self):
        """Load configuration from file"""
        file_path = filedialog.askopenfilename(
            title="Load Client Configuration",
            filetypes=[("JSON files", "*.json"), ("All files", "*.*")]
        )
        
        if file_path:
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    config_data = json.load(f)
                
                config = ClientConfig(**config_data)
                self.set_client_config(config)
                
                messagebox.showinfo("Success", f"Configuration loaded from {file_path}")
                self.status_var.set(f"Configuration loaded: {file_path}")
                logger.info(f"Configuration loaded from {file_path}")
                
            except Exception as e:
                error_msg = f"Error loading configuration: {str(e)}"
                messagebox.showerror("Load Error", error_msg)
                logger.error(error_msg)
    
    def reset_config(self):
        """Reset configuration to defaults"""
        if messagebox.askyesno("Reset Configuration", "Are you sure you want to reset all settings to defaults?"):
            self.load_default_config()
            self.locations_text.delete('1.0', tk.END)
            self.locations_text.insert('1.0', "Daphne\nMobile\nFoley")
            self.custom_css_text.delete('1.0', tk.END)
            self.single_location_var.set(False)
            self.enable_location_filtering_var.set(True)
            self.airtable_base_var.set("")
            self.status_var.set("Configuration reset to defaults")
    
    def preview_changes(self):
        """Preview the changes that will be made"""
        config = self.get_client_config()
        
        preview_window = tk.Toplevel(self.root)
        preview_window.title("Preview Changes")
        preview_window.geometry("800x600")
        
        # Create preview content
        preview_text = scrolledtext.ScrolledText(preview_window, wrap='word', font=('Consolas', 10))
        preview_text.pack(fill='both', expand=True, padx=10, pady=10)
        
        preview_content = f"""
DASHBOARD CUSTOMIZATION PREVIEW
{'=' * 50}

Business Information:
• Business Name: {config.business_name}
• Dashboard Title: {config.dashboard_title}
• Contact Email: {config.contact_email}

Branding Changes:
• Logo: {config.logo_path or 'Default logo will be used'}
• Primary Color: {config.primary_color}
• Secondary Color: {config.secondary_color}
• Font Family: {config.font_family}

Location Settings:
• Single Location Mode: {'Yes' if config.single_location_mode else 'No'}
• Enable Location Filtering: {'Yes' if config.enable_location_filtering else 'No'}
• Locations: {', '.join(config.locations) if config.locations else 'None specified'}

Airtable Configuration:
• Base ID: {config.airtable_base_id or 'Not specified'}

Files that will be modified:
• index.html - Business name, logo, location filters
• styles.css - Colors, fonts, custom styles
• script.js - Location names, filtering logic
• config.py - Airtable configuration, client settings

Custom CSS:
{config.custom_css or 'No custom CSS specified'}

Location-specific modifications:
"""
        
        if config.single_location_mode:
            preview_content += """
• Location filter dropdown will be hidden
• Location comparison features will be disabled
• Location-specific charts will show single location only
• Location breakdown cards will be hidden
"""
        else:
            preview_content += """
• All location features will remain active
• Location filtering will be available
• Multi-location charts and comparisons enabled
"""
        
        preview_text.insert('1.0', preview_content)
        preview_text.config(state='disabled')
        
        # Add close button
        ttk.Button(preview_window, text="Close", command=preview_window.destroy).pack(pady=10)

    def preview_html(self):
        """Preview the HTML with current customizations"""
        if not self.source_directory:
            messagebox.showerror("Error", "Please select a source directory first.")
            return

        config = self.get_client_config()

        if not config.business_name:
            messagebox.showwarning("Warning", "Please enter a business name for better preview.")

        try:
            # Read the original HTML file
            html_file = os.path.join(self.source_directory, 'index.html')
            if not os.path.exists(html_file):
                messagebox.showerror("Error", "index.html not found in source directory.")
                return

            with open(html_file, 'r', encoding='utf-8') as f:
                content = f.read()

            # Apply customizations to the content
            customized_content = self._apply_html_customizations(content, config)

            # Create preview window
            preview_window = tk.Toplevel(self.root)
            preview_window.title(f"HTML Preview - {config.business_name or 'Your Business'}")
            preview_window.geometry("1000x700")

            # Create notebook for different views
            preview_notebook = ttk.Notebook(preview_window)
            preview_notebook.pack(fill='both', expand=True, padx=10, pady=10)

            # HTML Source Tab
            source_frame = ttk.Frame(preview_notebook)
            preview_notebook.add(source_frame, text="📄 HTML Source")

            source_text = scrolledtext.ScrolledText(source_frame, wrap='word', font=('Consolas', 9))
            source_text.pack(fill='both', expand=True)
            source_text.insert('1.0', customized_content)

            # Header Preview Tab
            header_frame = ttk.Frame(preview_notebook)
            preview_notebook.add(header_frame, text="🎨 Header Preview")

            # Extract and show just the header section
            header_match = re.search(r'<header.*?</header>', customized_content, re.DOTALL)
            if header_match:
                header_html = header_match.group(0)

                # Create a simple preview
                preview_html = f"""
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>Header Preview</title>
    <style>
        body {{ font-family: {config.font_family}, sans-serif; margin: 0; padding: 20px; background: #f5f5f5; }}
        .container {{ max-width: 1200px; margin: 0 auto; }}
        .header-content {{ display: flex; align-items: center; justify-content: space-between; background: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); }}
        .header-content > div:first-child {{ display: flex; align-items: center; }}
        .logo img {{ height: 40px; }}
        .business-name-display {{ margin-left: 20px; display: flex; flex-direction: column; justify-content: center; }}
        .business-name {{ margin: 0; font-size: 1.8rem; font-weight: 700; color: {config.primary_color}; line-height: 1.2; }}
        .business-subtitle {{ margin: 0; font-size: 0.9rem; color: #666; font-weight: 500; }}
        .filter-container {{ display: flex; gap: 15px; align-items: center; }}
        .filter-group {{ display: flex; flex-direction: column; gap: 5px; }}
        .filter-group label {{ font-size: 0.9rem; font-weight: 500; color: #333; }}
        .filter-select {{ padding: 8px 12px; border: 1px solid #ddd; border-radius: 4px; background: white; }}
        .btn {{ padding: 8px 16px; background: {config.primary_color}; color: white; border: none; border-radius: 4px; cursor: pointer; }}
    </style>
</head>
<body>
    {header_html}
</body>
</html>
                """

                # Save temporary preview file
                import tempfile
                with tempfile.NamedTemporaryFile(mode='w', suffix='.html', delete=False, encoding='utf-8') as f:
                    f.write(preview_html)
                    temp_file = f.name

                # Create frame with buttons
                button_frame = ttk.Frame(header_frame)
                button_frame.pack(fill='x', pady=10)

                ttk.Button(button_frame, text="🌐 Open in Browser",
                          command=lambda: webbrowser.open(f'file://{temp_file}')).pack(side='left')
                ttk.Button(button_frame, text="📋 Copy HTML",
                          command=lambda: self._copy_to_clipboard(preview_html)).pack(side='left', padx=(10, 0))

                # Show preview text
                header_text = scrolledtext.ScrolledText(header_frame, wrap='word', font=('Consolas', 9))
                header_text.pack(fill='both', expand=True)
                header_text.insert('1.0', preview_html)

            # Changes Summary Tab
            changes_frame = ttk.Frame(preview_notebook)
            preview_notebook.add(changes_frame, text="📝 Changes Made")

            changes_text = scrolledtext.ScrolledText(changes_frame, wrap='word', font=('Consolas', 10))
            changes_text.pack(fill='both', expand=True)

            changes_summary = self._generate_html_changes_summary(config)
            changes_text.insert('1.0', changes_summary)

            # Action buttons
            action_frame = ttk.Frame(preview_window)
            action_frame.pack(fill='x', padx=10, pady=10)

            ttk.Button(action_frame, text="💾 Save Preview HTML",
                      command=lambda: self._save_preview_html(customized_content, config)).pack(side='left')
            ttk.Button(action_frame, text="Close", command=preview_window.destroy).pack(side='right')

        except Exception as e:
            error_msg = f"Error generating HTML preview: {str(e)}"
            messagebox.showerror("Preview Error", error_msg)
            logger.error(error_msg)

    def _apply_html_customizations(self, content: str, config: ClientConfig) -> str:
        """Apply HTML customizations to content"""
        # Replace business name and title
        content = re.sub(r'RepairLift Attribution Logo', f'{config.business_name} Attribution Logo', content)
        content = re.sub(r'<title>.*?</title>', f'<title>{config.dashboard_title or config.business_name + " Attribution Dashboard"}</title>', content)

        # Add business name display to header (after logo)
        header_business_name = f'''<div class="business-name-display">
                <h2 class="business-name">{config.business_name}</h2>
                <p class="business-subtitle">Attribution Dashboard</p>
            </div>'''

        # Insert business name after logo div
        content = re.sub(
            r'(<div class="logo">.*?</div>)',
            r'\1' + header_business_name,
            content,
            flags=re.DOTALL
        )

        # Replace logo if provided
        if config.logo_path:
            content = re.sub(r'src="img/rl\.svg"', f'src="{config.logo_path}"', content)

        # Handle single-location mode - hide all filters and simplify header
        if config.single_location_mode:
            # Hide the entire filter container for single-location clients
            content = re.sub(
                r'<div class="filter-container">.*?</div>\s*</div>\s*</div>',
                '''<div class="single-location-header">
                    <div class="location-badge">{}</div>
                </div>
            </div>
        </div>'''.format(config.locations[0] if config.locations else "Main Location"),
                content,
                flags=re.DOTALL
            )

        # Replace hardcoded location names with client locations
        if config.locations:
            for i, location in enumerate(['Daphne', 'Mobile', 'Foley']):
                if i < len(config.locations):
                    content = content.replace(f'Quick Fix - {location}', f'{config.business_name} - {config.locations[i]}')
                    content = content.replace(f'>{location}<', f'>{config.locations[i]}<')

        return content

    def _copy_to_clipboard(self, text: str):
        """Copy text to clipboard"""
        self.root.clipboard_clear()
        self.root.clipboard_append(text)
        messagebox.showinfo("Copied", "HTML content copied to clipboard!")

    def _save_preview_html(self, content: str, config: ClientConfig):
        """Save preview HTML to file"""
        file_path = filedialog.asksaveasfilename(
            title="Save Preview HTML",
            defaultextension=".html",
            filetypes=[("HTML files", "*.html"), ("All files", "*.*")],
            initialname=f"{config.business_name.replace(' ', '_')}_preview.html" if config.business_name else "dashboard_preview.html"
        )

        if file_path:
            try:
                with open(file_path, 'w', encoding='utf-8') as f:
                    f.write(content)

                messagebox.showinfo("Success", f"Preview HTML saved to {file_path}")

                # Ask if user wants to open it
                if messagebox.askyesno("Open File", "Would you like to open the saved HTML file in your browser?"):
                    webbrowser.open(f'file://{file_path}')

            except Exception as e:
                messagebox.showerror("Error", f"Failed to save HTML: {str(e)}")

    def _generate_html_changes_summary(self, config: ClientConfig) -> str:
        """Generate a summary of HTML changes"""
        summary = f"""HTML CUSTOMIZATION SUMMARY
{'=' * 50}

Business Information:
• Business Name: {config.business_name or 'Not specified'}
• Dashboard Title: {config.dashboard_title or 'Default title'}

Header Changes:
• Logo alt text: "RepairLift Attribution Logo" → "{config.business_name} Attribution Logo"
• Added business name display next to logo
• Business name styled with primary color: {config.primary_color}

Page Title:
• Changed to: "{config.dashboard_title or config.business_name + ' Attribution Dashboard'}"

Location Customizations:
"""

        if config.single_location_mode:
            summary += "• Location filter dropdown: HIDDEN\n"
            summary += "• Location-specific elements: HIDDEN\n"
        else:
            summary += "• Location filter dropdown: VISIBLE\n"
            if config.locations:
                summary += f"• Location options updated to: {', '.join(config.locations)}\n"

        if config.locations:
            summary += "\nLocation Name Replacements:\n"
            default_locations = ['Daphne', 'Mobile', 'Foley']
            for i, location in enumerate(default_locations):
                if i < len(config.locations):
                    summary += f"• 'Quick Fix - {location}' → '{config.business_name} - {config.locations[i]}'\n"

        summary += f"\nLogo Changes:\n"
        if config.logo_path:
            summary += f"• Logo source: {config.logo_path}\n"
        else:
            summary += "• Logo: Using default (img/rl.svg)\n"

        summary += f"\nStyling Applied:\n"
        summary += f"• Primary color: {config.primary_color}\n"
        summary += f"• Font family: {config.font_family}\n"

        if config.custom_css:
            summary += f"\nCustom CSS Added:\n{config.custom_css}\n"

        return summary

    def open_live_preview(self):
        """Open a real dashboard preview using the actual source files"""
        if not self.source_directory:
            messagebox.showerror("Error", "Please select a source directory first.")
            return

        # Check if required files exist
        required_files = ['index.html', 'styles.css', 'script.js']
        missing_files = []
        for file in required_files:
            if not os.path.exists(os.path.join(self.source_directory, file)):
                missing_files.append(file)

        if missing_files:
            messagebox.showerror("Error", f"Missing required files: {', '.join(missing_files)}")
            return

        # Create preview window
        preview_window = tk.Toplevel(self.root)
        preview_window.title("🖥️ Real Dashboard Preview")
        preview_window.geometry("1400x900")
        preview_window.minsize(1200, 700)

        # Create main frame
        main_frame = ttk.Frame(preview_window)
        main_frame.pack(fill='both', expand=True, padx=10, pady=10)

        # Control panel
        control_frame = ttk.LabelFrame(main_frame, text="Real Dashboard Preview Controls", padding=10)
        control_frame.pack(fill='x', pady=(0, 10))

        # Preview controls
        ttk.Button(control_frame, text="🔄 Apply Changes & Preview",
                  command=self.apply_and_preview, style='Accent.TButton').pack(side='left')

        ttk.Button(control_frame, text="🌐 Open in Browser",
                  command=self.open_real_preview_in_browser).pack(side='left', padx=(10, 0))

        ttk.Button(control_frame, text="🔙 Restore Original",
                  command=self.restore_original_files).pack(side='left', padx=(10, 0))

        # Status
        self.preview_status_var = tk.StringVar(value="Ready to preview - Click 'Apply Changes & Preview'")
        ttk.Label(control_frame, textvariable=self.preview_status_var,
                 font=('Arial', 9), foreground='blue').pack(side='right')

        # Instructions
        instructions_frame = ttk.LabelFrame(main_frame, text="How it Works", padding=10)
        instructions_frame.pack(fill='x', pady=(0, 10))

        instructions_text = """
🎯 Real Dashboard Preview:
1. Click 'Apply Changes & Preview' to temporarily apply your customizations to the actual dashboard files
2. Click 'Open in Browser' to see the real dashboard with your changes
3. Click 'Restore Original' to undo the temporary changes when done

⚠️ Note: This temporarily modifies your source files for preview. Always restore when finished!
        """.strip()

        ttk.Label(instructions_frame, text=instructions_text,
                 font=('Arial', 10), justify='left').pack(anchor='w')

        # Preview info area
        info_frame = ttk.LabelFrame(main_frame, text="Preview Information", padding=10)
        info_frame.pack(fill='both', expand=True)

        self.preview_info_text = scrolledtext.ScrolledText(info_frame, height=15, wrap='word', font=('Consolas', 9))
        self.preview_info_text.pack(fill='both', expand=True)

        # Store references
        self.live_preview_window = preview_window
        self.preview_backup_dir = None

        # Show initial info
        self.show_preview_info()

    def apply_and_preview(self):
        """Apply customizations to actual files for preview"""
        try:
            config = self.get_client_config()

            if not config.business_name:
                messagebox.showwarning("Warning", "Please enter a business name first.")
                return

            # Create backup of original files
            self.create_preview_backup()

            # Apply customizations to actual files
            self.preview_status_var.set("Applying customizations...")
            self.live_preview_window.update()

            # Customize the actual files temporarily
            self.customizer._customize_html_files(self.source_directory, config)
            self.customizer._customize_css_files(self.source_directory, config)
            self.customizer._customize_js_files(self.source_directory, config)

            self.preview_status_var.set("✅ Customizations applied! Click 'Open in Browser' to see the result.")

            # Update preview info
            self.update_preview_info(config)

            # Auto-open in browser
            if messagebox.askyesno("Preview Ready",
                                  "Customizations applied successfully!\n\n"
                                  "Would you like to open the dashboard in your browser now?"):
                self.open_real_preview_in_browser()

        except Exception as e:
            error_msg = f"Error applying preview: {str(e)}"
            self.preview_status_var.set(f"❌ Error: {str(e)}")
            messagebox.showerror("Preview Error", error_msg)
            logger.error(error_msg)

    def create_preview_backup(self):
        """Create backup of original files before preview"""
        try:
            # Create backup directory
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            self.preview_backup_dir = os.path.join(self.source_directory, f"_preview_backup_{timestamp}")
            os.makedirs(self.preview_backup_dir, exist_ok=True)

            # Backup key files
            files_to_backup = ['index.html', 'styles.css', 'script.js', 'config.py']

            for filename in files_to_backup:
                source_file = os.path.join(self.source_directory, filename)
                if os.path.exists(source_file):
                    backup_file = os.path.join(self.preview_backup_dir, filename)
                    shutil.copy2(source_file, backup_file)

            logger.info(f"Preview backup created: {self.preview_backup_dir}")

        except Exception as e:
            logger.error(f"Error creating preview backup: {e}")
            raise

    def restore_original_files(self):
        """Restore original files from backup"""
        try:
            if not self.preview_backup_dir or not os.path.exists(self.preview_backup_dir):
                messagebox.showwarning("Warning", "No backup found to restore from.")
                return

            if not messagebox.askyesno("Restore Original",
                                      "This will restore the original files and undo all preview changes.\n\n"
                                      "Continue?"):
                return

            # Restore files from backup
            for filename in os.listdir(self.preview_backup_dir):
                backup_file = os.path.join(self.preview_backup_dir, filename)
                original_file = os.path.join(self.source_directory, filename)

                if os.path.exists(backup_file):
                    shutil.copy2(backup_file, original_file)

            # Clean up backup directory
            shutil.rmtree(self.preview_backup_dir)
            self.preview_backup_dir = None

            self.preview_status_var.set("✅ Original files restored successfully!")
            messagebox.showinfo("Restored", "Original files have been restored.")

            # Update preview info
            self.show_preview_info()

        except Exception as e:
            error_msg = f"Error restoring files: {str(e)}"
            self.preview_status_var.set(f"❌ Error restoring: {str(e)}")
            messagebox.showerror("Restore Error", error_msg)
            logger.error(error_msg)

    def open_real_preview_in_browser(self):
        """Open the actual dashboard in browser"""
        try:
            # Check if index.html exists
            index_file = os.path.join(self.source_directory, 'index.html')
            if not os.path.exists(index_file):
                messagebox.showerror("Error", "index.html not found in source directory.")
                return

            # Open the actual dashboard file
            file_url = f'file://{os.path.abspath(index_file)}'
            webbrowser.open(file_url)

            self.preview_status_var.set("🌐 Dashboard opened in browser")

            # Show instructions
            messagebox.showinfo("Dashboard Opened",
                              "The real dashboard has been opened in your browser!\n\n"
                              "You can see your actual customizations applied.\n\n"
                              "Remember to click 'Restore Original' when you're done previewing.")

        except Exception as e:
            error_msg = f"Error opening dashboard: {str(e)}"
            messagebox.showerror("Browser Error", error_msg)
            logger.error(error_msg)

    def show_preview_info(self):
        """Show initial preview information"""
        config = self.get_client_config()

        info_text = f"""
REAL DASHBOARD PREVIEW
{'=' * 50}

Current Configuration:
• Business Name: {config.business_name or 'Not set'}
• Dashboard Title: {config.dashboard_title or 'Default'}
• Primary Color: {config.primary_color}
• Secondary Color: {config.secondary_color}
• Font Family: {config.font_family}
• Single Location Mode: {'Yes' if config.single_location_mode else 'No'}
• Locations: {', '.join(config.locations) if config.locations else 'Default'}

How Real Preview Works:
1. Creates backup of your original files
2. Temporarily applies your customizations to the actual dashboard files
3. Opens the real dashboard in your browser
4. You can restore original files when done

Status: Ready to apply customizations
        """.strip()

        self.preview_info_text.delete('1.0', tk.END)
        self.preview_info_text.insert('1.0', info_text)

    def update_preview_info(self, config: ClientConfig):
        """Update preview info after applying changes"""
        info_text = f"""
REAL DASHBOARD PREVIEW - CUSTOMIZATIONS APPLIED
{'=' * 60}

Applied Customizations:
• Business Name: "{config.business_name}" → Replaced "RepairLift" in header
• Dashboard Title: "{config.dashboard_title or config.business_name + ' Attribution Dashboard'}" → Updated page title
• Primary Color: {config.primary_color} → Applied to business name and buttons
• Secondary Color: {config.secondary_color} → Applied to hover effects
• Font Family: {config.font_family} → Applied throughout dashboard

Header Changes:
• Logo alt text updated to "{config.business_name} Attribution Logo"
• Business name display added next to logo
• Styled with your chosen colors and fonts

Location Settings:
• Single Location Mode: {'Enabled' if config.single_location_mode else 'Disabled'}
"""

        if config.single_location_mode:
            location_name = config.locations[0] if config.locations else "Main Location"
            info_text += f"""• All filters hidden for clean interface
• Location badge shows: "{location_name}"
• Location-specific charts disabled
"""
        else:
            info_text += f"""• Multi-location filters visible
• Location options: {', '.join(config.locations) if config.locations else 'Default locations'}
• All location features enabled
"""

        if config.locations:
            info_text += f"""
Location Name Replacements:
"""
            default_locations = ['Daphne', 'Mobile', 'Foley']
            for i, location in enumerate(default_locations):
                if i < len(config.locations):
                    info_text += f"• 'Quick Fix - {location}' → '{config.business_name} - {config.locations[i]}'\n"

        info_text += f"""
Files Modified:
• index.html - Business name, logo, location filters
• styles.css - Colors, fonts, custom styles
• script.js - Location names, filtering logic

Backup Location: {self.preview_backup_dir or 'Not created yet'}

Status: ✅ Customizations applied - Open in browser to see results!
        """

        self.preview_info_text.delete('1.0', tk.END)
        self.preview_info_text.insert('1.0', info_text)

    def open_realtime_preview(self):
        """Open real-time preview that updates as you type"""
        if not self.source_directory:
            messagebox.showerror("Error", "Please select a source directory first.")
            return

        # Create real-time preview window
        preview_window = tk.Toplevel(self.root)
        preview_window.title("⚡ Real-Time Dashboard Preview")
        preview_window.geometry("1600x1000")
        preview_window.minsize(1400, 800)

        # Create main paned window (split view)
        main_paned = ttk.PanedWindow(preview_window, orient='horizontal')
        main_paned.pack(fill='both', expand=True, padx=5, pady=5)

        # Left panel - Quick settings
        left_frame = ttk.LabelFrame(main_paned, text="⚡ Live Settings", padding=10)
        main_paned.add(left_frame, weight=1)

        # Right panel - Live preview
        right_frame = ttk.LabelFrame(main_paned, text="🖥️ Live Preview", padding=5)
        main_paned.add(right_frame, weight=3)

        # Create quick settings panel
        self.create_quick_settings_panel(left_frame)

        # Create live preview panel
        self.create_live_preview_panel(right_frame)

        # Store reference
        self.realtime_preview_window = preview_window

        # Initial preview
        self.update_realtime_preview()

        # Setup real-time updates
        self.setup_realtime_updates()

    def create_quick_settings_panel(self, parent):
        """Create quick settings panel for real-time editing"""
        # Business name
        ttk.Label(parent, text="Business Name:", font=('Arial', 10, 'bold')).pack(anchor='w', pady=(0, 5))
        self.rt_business_name = tk.StringVar()
        self.rt_business_name.set(self.business_name_var.get())
        business_entry = ttk.Entry(parent, textvariable=self.rt_business_name, font=('Arial', 11))
        business_entry.pack(fill='x', pady=(0, 15))

        # Primary color
        ttk.Label(parent, text="Primary Color:", font=('Arial', 10, 'bold')).pack(anchor='w', pady=(0, 5))
        color_frame = ttk.Frame(parent)
        color_frame.pack(fill='x', pady=(0, 15))

        self.rt_primary_color = tk.StringVar()
        self.rt_primary_color.set(self.primary_color_var.get())
        color_entry = ttk.Entry(color_frame, textvariable=self.rt_primary_color, width=10)
        color_entry.pack(side='left')

        self.rt_color_preview = tk.Label(color_frame, width=3, bg=self.rt_primary_color.get())
        self.rt_color_preview.pack(side='left', padx=(5, 0))

        # Font family
        ttk.Label(parent, text="Font Family:", font=('Arial', 10, 'bold')).pack(anchor='w', pady=(0, 5))
        self.rt_font_family = tk.StringVar()
        self.rt_font_family.set(self.font_family_var.get())
        font_combo = ttk.Combobox(parent, textvariable=self.rt_font_family,
                                 values=["Inter", "Arial", "Helvetica", "Roboto", "Open Sans", "Lato"])
        font_combo.pack(fill='x', pady=(0, 15))

        # Single location mode
        self.rt_single_location = tk.BooleanVar()
        self.rt_single_location.set(self.single_location_var.get())
        ttk.Checkbutton(parent, text="Single Location Mode",
                       variable=self.rt_single_location).pack(anchor='w', pady=(0, 15))

        # Location name (for single location)
        ttk.Label(parent, text="Location Name:", font=('Arial', 10, 'bold')).pack(anchor='w', pady=(0, 5))
        self.rt_location_name = tk.StringVar()
        locations = self.locations_text.get('1.0', tk.END).strip().split('\n')
        self.rt_location_name.set(locations[0] if locations and locations[0] else "Main Location")
        location_entry = ttk.Entry(parent, textvariable=self.rt_location_name)
        location_entry.pack(fill='x', pady=(0, 15))

        # Preview controls
        controls_frame = ttk.LabelFrame(parent, text="Preview Controls", padding=10)
        controls_frame.pack(fill='x', pady=(10, 0))

        ttk.Button(controls_frame, text="🔄 Refresh Preview",
                  command=self.update_realtime_preview).pack(fill='x', pady=2)
        ttk.Button(controls_frame, text="🌐 Open in Browser",
                  command=self.open_realtime_in_browser).pack(fill='x', pady=2)
        ttk.Button(controls_frame, text="📋 Copy HTML",
                  command=self.copy_realtime_html).pack(fill='x', pady=2)

        # Auto-update toggle
        self.rt_auto_update = tk.BooleanVar(value=True)
        ttk.Checkbutton(controls_frame, text="Auto-update (live)",
                       variable=self.rt_auto_update).pack(anchor='w', pady=(10, 0))

    def create_live_preview_panel(self, parent):
        """Create the live preview display panel"""
        # Create notebook for different preview modes
        self.rt_notebook = ttk.Notebook(parent)
        self.rt_notebook.pack(fill='both', expand=True)

        # Visual preview tab
        visual_frame = ttk.Frame(self.rt_notebook)
        self.rt_notebook.add(visual_frame, text="🖥️ Visual Preview")

        # Create HTML display area
        html_frame = ttk.Frame(visual_frame)
        html_frame.pack(fill='both', expand=True, padx=5, pady=5)

        # Instructions
        info_frame = ttk.Frame(html_frame)
        info_frame.pack(fill='x', pady=(0, 10))

        ttk.Label(info_frame, text="⚡ Real-time preview updates as you type!",
                 font=('Arial', 11, 'bold'), foreground='green').pack()
        ttk.Label(info_frame, text="Changes appear instantly - no need to apply or restore files",
                 font=('Arial', 9), foreground='gray').pack()

        # Browser button
        browser_frame = ttk.Frame(html_frame)
        browser_frame.pack(fill='x', pady=(0, 10))

        ttk.Button(browser_frame, text="🌐 Open Live Preview in Browser",
                  command=self.open_realtime_in_browser,
                  style='Accent.TButton').pack()

        # HTML source tab
        source_frame = ttk.Frame(self.rt_notebook)
        self.rt_notebook.add(source_frame, text="📄 HTML Source")

        self.rt_html_text = scrolledtext.ScrolledText(source_frame, wrap='word', font=('Consolas', 9))
        self.rt_html_text.pack(fill='both', expand=True, padx=5, pady=5)

        # CSS tab
        css_frame = ttk.Frame(self.rt_notebook)
        self.rt_notebook.add(css_frame, text="🎨 CSS Styles")

        self.rt_css_text = scrolledtext.ScrolledText(css_frame, wrap='word', font=('Consolas', 9))
        self.rt_css_text.pack(fill='both', expand=True, padx=5, pady=5)

    def setup_realtime_updates(self):
        """Setup real-time updates when settings change"""
        # Bind all variables to update function
        self.rt_business_name.trace('w', lambda *args: self.on_realtime_change())
        self.rt_primary_color.trace('w', lambda *args: self.on_realtime_change())
        self.rt_font_family.trace('w', lambda *args: self.on_realtime_change())
        self.rt_single_location.trace('w', lambda *args: self.on_realtime_change())
        self.rt_location_name.trace('w', lambda *args: self.on_realtime_change())

    def on_realtime_change(self):
        """Called when any setting changes"""
        if hasattr(self, 'rt_auto_update') and self.rt_auto_update.get():
            # Update color preview
            try:
                self.rt_color_preview.config(bg=self.rt_primary_color.get())
            except tk.TclError:
                self.rt_color_preview.config(bg='white')

            # Update preview after short delay to avoid too many updates
            if hasattr(self, 'realtime_preview_window'):
                self.realtime_preview_window.after(300, self.update_realtime_preview)

    def update_realtime_preview(self):
        """Update the real-time preview"""
        try:
            # Generate current HTML
            html_content = self.generate_realtime_html()

            # Update HTML source display
            if hasattr(self, 'rt_html_text'):
                self.rt_html_text.delete('1.0', tk.END)
                self.rt_html_text.insert('1.0', html_content)

            # Update CSS display
            if hasattr(self, 'rt_css_text'):
                css_content = self.generate_realtime_css()
                self.rt_css_text.delete('1.0', tk.END)
                self.rt_css_text.insert('1.0', css_content)

            # Save current HTML for browser opening
            self.current_realtime_html = html_content

        except Exception as e:
            logger.error(f"Error updating real-time preview: {e}")

    def generate_realtime_html(self):
        """Generate HTML with current real-time settings"""
        business_name = self.rt_business_name.get() or "Your Business"
        primary_color = self.rt_primary_color.get()
        font_family = self.rt_font_family.get()
        single_location = self.rt_single_location.get()
        location_name = self.rt_location_name.get() or "Main Location"

        # Generate header based on mode
        if single_location:
            header_right = f'<div class="location-badge">{location_name}</div>'
        else:
            header_right = '''
                <div class="filter-container">
                    <div class="filter-group">
                        <label for="date-filter">Date:</label>
                        <select id="date-filter" class="filter-select">
                            <option value="all">All Dates</option>
                            <option value="today">Today</option>
                        </select>
                    </div>
                    <div class="filter-group">
                        <label for="location-filter">Location:</label>
                        <select id="location-filter" class="filter-select">
                            <option value="all">All Locations</option>
                            <option value="location1">Location 1</option>
                        </select>
                    </div>
                    <button class="btn custom-range-btn">Custom Range</button>
                </div>
            '''

        return f'''
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{business_name} Attribution Dashboard</title>
    <style>{self.generate_realtime_css()}</style>
</head>
<body>
    <div class="container">
        <header>
            <div class="header-content">
                <div class="header-left">
                    <div class="logo">
                        <img src="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNDAiIGhlaWdodD0iNDAiIHZpZXdCb3g9IjAgMCA0MCA0MCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHJlY3Qgd2lkdGg9IjQwIiBoZWlnaHQ9IjQwIiByeD0iOCIgZmlsbD0iIzMzNzNkYyIvPgo8dGV4dCB4PSIyMCIgeT0iMjYiIGZvbnQtZmFtaWx5PSJBcmlhbCIgZm9udC1zaXplPSIxOCIgZm9udC13ZWlnaHQ9ImJvbGQiIGZpbGw9IndoaXRlIiB0ZXh0LWFuY2hvcj0ibWlkZGxlIj5MPC90ZXh0Pgo8L3N2Zz4K"
                             alt="{business_name} Attribution Logo" />
                    </div>
                    <div class="business-name-display">
                        <h2 class="business-name">{business_name}</h2>
                        <p class="business-subtitle">Attribution Dashboard</p>
                    </div>
                </div>
                <div class="header-right">
                    {header_right}
                </div>
            </div>
        </header>

        <div class="dashboard-content">
            <div class="stats-grid">
                <div class="stat-card">
                    <div class="stat-value">$12,450</div>
                    <div class="stat-label">Total Revenue</div>
                </div>
                <div class="stat-card">
                    <div class="stat-value">156</div>
                    <div class="stat-label">Transactions</div>
                </div>
                <div class="stat-card">
                    <div class="stat-value">$79.81</div>
                    <div class="stat-label">Avg Order Value</div>
                </div>
                <div class="stat-card">
                    <div class="stat-value">4.2%</div>
                    <div class="stat-label">Conversion Rate</div>
                </div>
            </div>

            <div class="preview-notice">
                <h3>⚡ Real-Time Preview</h3>
                <p>This preview updates instantly as you change settings on the left!</p>
                <p><strong>Business:</strong> {business_name} | <strong>Mode:</strong> {"Single Location" if single_location else "Multi Location"}</p>
            </div>
        </div>
    </div>
</body>
</html>
        '''

    def generate_realtime_css(self):
        """Generate CSS for real-time preview"""
        primary_color = self.rt_primary_color.get()
        font_family = self.rt_font_family.get()

        return f'''
        * {{
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }}

        body {{
            font-family: {font_family}, sans-serif;
            background: #f5f7fa;
            color: #333;
            line-height: 1.6;
        }}

        .container {{
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }}

        header {{
            background: white;
            border-radius: 12px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            margin-bottom: 20px;
        }}

        .header-content {{
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 20px 30px;
        }}

        .header-left {{
            display: flex;
            align-items: center;
            gap: 20px;
        }}

        .logo img {{
            height: 40px;
            width: 40px;
        }}

        .business-name-display {{
            display: flex;
            flex-direction: column;
        }}

        .business-name {{
            font-size: 1.8rem;
            font-weight: 700;
            color: {primary_color};
            margin: 0;
            line-height: 1.2;
        }}

        .business-subtitle {{
            font-size: 0.9rem;
            color: #666;
            font-weight: 500;
            margin: 0;
        }}

        .header-right {{
            display: flex;
            align-items: center;
            gap: 20px;
        }}

        .location-badge {{
            background: {primary_color};
            color: white;
            padding: 8px 16px;
            border-radius: 20px;
            font-size: 0.9rem;
            font-weight: 600;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }}

        .filter-container {{
            display: flex;
            gap: 15px;
            align-items: center;
        }}

        .filter-group {{
            display: flex;
            flex-direction: column;
            gap: 5px;
        }}

        .filter-group label {{
            font-size: 0.9rem;
            font-weight: 500;
            color: #333;
        }}

        .filter-select {{
            padding: 8px 12px;
            border: 1px solid #ddd;
            border-radius: 6px;
            background: white;
            font-size: 0.9rem;
            min-width: 120px;
        }}

        .btn {{
            padding: 8px 16px;
            background: {primary_color};
            color: white;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            font-size: 0.9rem;
            font-weight: 500;
            transition: background-color 0.2s;
        }}

        .dashboard-content {{
            background: white;
            border-radius: 12px;
            padding: 20px;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }}

        .stats-grid {{
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }}

        .stat-card {{
            background: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
            text-align: center;
            border-left: 4px solid {primary_color};
        }}

        .stat-value {{
            font-size: 2rem;
            font-weight: bold;
            color: {primary_color};
            margin-bottom: 5px;
        }}

        .stat-label {{
            color: #666;
            font-size: 0.9rem;
        }}

        .preview-notice {{
            background: linear-gradient(135deg, {primary_color}15, {primary_color}05);
            padding: 20px;
            border-radius: 8px;
            border: 1px solid {primary_color}30;
        }}

        .preview-notice h3 {{
            color: {primary_color};
            margin-bottom: 10px;
        }}
        '''

    def open_realtime_in_browser(self):
        """Open real-time preview in browser"""
        if hasattr(self, 'current_realtime_html'):
            # Save to temp file
            with tempfile.NamedTemporaryFile(mode='w', suffix='.html', delete=False, encoding='utf-8') as f:
                f.write(self.current_realtime_html)
                temp_file = f.name

            # Open in browser
            webbrowser.open(f'file://{temp_file}')
        else:
            messagebox.showwarning("Warning", "No preview available. Please wait for preview to load.")

    def copy_realtime_html(self):
        """Copy real-time HTML to clipboard"""
        if hasattr(self, 'current_realtime_html'):
            self.realtime_preview_window.clipboard_clear()
            self.realtime_preview_window.clipboard_append(self.current_realtime_html)
            messagebox.showinfo("Copied", "HTML copied to clipboard!")
        else:
            messagebox.showwarning("Warning", "No HTML available to copy.")

    def open_web_preview(self):
        """Open web preview using temp directory and local server"""
        if not self.source_directory:
            messagebox.showerror("Error", "Please select a source directory first.")
            return

        # Check if required files exist
        required_files = ['index.html', 'styles.css', 'script.js']
        missing_files = []
        for file in required_files:
            if not os.path.exists(os.path.join(self.source_directory, file)):
                missing_files.append(file)

        if missing_files:
            messagebox.showerror("Error", f"Missing required files: {', '.join(missing_files)}")
            return

        # Create web preview window
        preview_window = tk.Toplevel(self.root)
        preview_window.title("🌐 Live Web Preview")
        preview_window.geometry("1400x900")
        preview_window.minsize(1200, 700)

        # Create main frame
        main_frame = ttk.Frame(preview_window)
        main_frame.pack(fill='both', expand=True, padx=10, pady=10)

        # Control panel
        control_frame = ttk.LabelFrame(main_frame, text="Web Preview Controls", padding=10)
        control_frame.pack(fill='x', pady=(0, 10))

        # Status
        self.web_preview_status = tk.StringVar(value="Ready to start web preview...")
        ttk.Label(control_frame, textvariable=self.web_preview_status,
                 font=('Arial', 10), foreground='blue').pack(side='left')

        # Controls
        ttk.Button(control_frame, text="🚀 Start Web Preview",
                  command=self.start_web_preview, style='Accent.TButton').pack(side='right', padx=(5, 0))
        ttk.Button(control_frame, text="🔄 Update Preview",
                  command=self.update_web_preview).pack(side='right', padx=(5, 0))
        ttk.Button(control_frame, text="🛑 Stop Preview",
                  command=self.stop_web_preview).pack(side='right', padx=(5, 0))

        # Instructions
        instructions_frame = ttk.LabelFrame(main_frame, text="How Web Preview Works", padding=10)
        instructions_frame.pack(fill='x', pady=(0, 10))

        instructions_text = """
🌐 Live Web Preview Process:
1. Creates a temporary copy of your dashboard files
2. Applies your customizations to the temp files
3. Starts a local web server to serve the files
4. Opens the REAL dashboard in your browser with your changes applied
5. Updates the preview when you click 'Update Preview'

✅ Safe: Uses temporary files, never modifies your originals
🔄 Live: Click 'Update Preview' to see new changes
🌐 Real: Shows the actual web app, not a simulation
        """.strip()

        ttk.Label(instructions_frame, text=instructions_text,
                 font=('Arial', 10), justify='left').pack(anchor='w')

        # Preview info area
        info_frame = ttk.LabelFrame(main_frame, text="Preview Information", padding=10)
        info_frame.pack(fill='both', expand=True)

        self.web_preview_info = scrolledtext.ScrolledText(info_frame, height=15, wrap='word', font=('Consolas', 9))
        self.web_preview_info.pack(fill='both', expand=True)

        # Store references
        self.web_preview_window = preview_window
        self.web_server = None
        self.web_server_thread = None
        self.temp_preview_dir = None

        # Show initial info
        self.show_web_preview_info()

        # Handle window close
        preview_window.protocol("WM_DELETE_WINDOW", self.on_web_preview_close)

    def start_web_preview(self):
        """Start the web preview server"""
        try:
            config = self.get_client_config()

            if not config.business_name:
                messagebox.showwarning("Warning", "Please enter a business name first.")
                return

            self.web_preview_status.set("Creating temporary files...")
            self.web_preview_window.update()

            # Create temp directory and copy files
            self.create_temp_preview_files(config)

            # Start local web server
            self.web_preview_status.set("Starting web server...")
            self.web_preview_window.update()

            port = self.start_local_server()

            if port:
                # Open in browser
                preview_url = f"http://localhost:{port}"
                webbrowser.open(preview_url)

                self.web_preview_status.set(f"✅ Web preview running at http://localhost:{port}")

                # Update info
                self.update_web_preview_info(config, port)

                messagebox.showinfo("Web Preview Started",
                                  f"Web preview is now running!\n\n"
                                  f"URL: http://localhost:{port}\n\n"
                                  f"The REAL dashboard with your customizations has been opened in your browser.\n\n"
                                  f"Click 'Update Preview' to apply new changes.")
            else:
                self.web_preview_status.set("❌ Failed to start web server")

        except Exception as e:
            error_msg = f"Error starting web preview: {str(e)}"
            self.web_preview_status.set(f"❌ Error: {str(e)}")
            messagebox.showerror("Web Preview Error", error_msg)
            logger.error(error_msg)

    def create_temp_preview_files(self, config: ClientConfig):
        """Create temporary files with customizations applied"""
        # Create temp directory
        self.temp_preview_dir = tempfile.mkdtemp(prefix="dashboard_preview_")

        # Copy all files from source to temp
        for item in os.listdir(self.source_directory):
            source_path = os.path.join(self.source_directory, item)
            dest_path = os.path.join(self.temp_preview_dir, item)

            if os.path.isfile(source_path):
                shutil.copy2(source_path, dest_path)
            elif os.path.isdir(source_path) and item not in ['.git', '__pycache__', 'logs']:
                shutil.copytree(source_path, dest_path, dirs_exist_ok=True)

        # Apply customizations to temp files
        self.customizer._customize_html_files(self.temp_preview_dir, config)
        self.customizer._customize_css_files(self.temp_preview_dir, config)
        self.customizer._customize_js_files(self.temp_preview_dir, config)

        logger.info(f"Temp preview files created: {self.temp_preview_dir}")

    def start_local_server(self):
        """Start local HTTP server for preview with improved error handling"""
        try:
            # Find available port
            port = self.find_free_port()
            logger.info(f"Found available port: {port}")

            # Store original directory
            self.original_dir = os.getcwd()

            # Change to temp directory
            if not os.path.exists(self.temp_preview_dir):
                raise Exception(f"Temp directory does not exist: {self.temp_preview_dir}")

            os.chdir(self.temp_preview_dir)
            logger.info(f"Changed to temp directory: {self.temp_preview_dir}")

            # Create custom handler to suppress logs
            class QuietHTTPRequestHandler(http.server.SimpleHTTPRequestHandler):
                def log_message(self, format, *args):
                    # Suppress HTTP request logs
                    pass

            # Create server with retry logic
            max_retries = 3
            for attempt in range(max_retries):
                try:
                    self.web_server = socketserver.TCPServer(("", port), QuietHTTPRequestHandler)
                    self.web_server.allow_reuse_address = True
                    break
                except OSError as e:
                    if attempt < max_retries - 1:
                        logger.warning(f"Port {port} busy, trying new port (attempt {attempt + 1})")
                        port = self.find_free_port()
                    else:
                        raise e

            # Start server in background thread
            def server_runner():
                try:
                    logger.info(f"Starting web server on port {port}")
                    self.web_server.serve_forever()
                except Exception as e:
                    logger.error(f"Web server error: {e}")

            self.web_server_thread = threading.Thread(target=server_runner, daemon=True)
            self.web_server_thread.start()

            # Verify server started
            import time
            time.sleep(0.5)  # Give server time to start

            if self.web_server_thread.is_alive():
                logger.info(f"Web server started successfully on port {port}")
                return port
            else:
                raise Exception("Web server failed to start")

        except Exception as e:
            logger.error(f"Error starting web server: {e}")
            # Restore original directory on error
            try:
                if hasattr(self, 'original_dir'):
                    os.chdir(self.original_dir)
            except:
                pass
            return None

    def find_free_port(self):
        """Find a free port for the web server"""
        with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as s:
            s.bind(('', 0))
            s.listen(1)
            port = s.getsockname()[1]
        return port

    def update_web_preview(self):
        """Update the web preview with current settings"""
        if not self.temp_preview_dir or not os.path.exists(self.temp_preview_dir):
            messagebox.showwarning("Warning", "Web preview not started. Please start preview first.")
            return

        try:
            config = self.get_client_config()

            self.web_preview_status.set("Updating preview...")
            self.web_preview_window.update()

            # Apply new customizations to temp files
            self.customizer._customize_html_files(self.temp_preview_dir, config)
            self.customizer._customize_css_files(self.temp_preview_dir, config)
            self.customizer._customize_js_files(self.temp_preview_dir, config)

            self.web_preview_status.set("✅ Preview updated! Refresh your browser to see changes.")

            # Update info
            port = self.get_server_port()
            self.update_web_preview_info(config, port)

            messagebox.showinfo("Preview Updated",
                              "Preview has been updated with your latest settings!\n\n"
                              "Refresh your browser (F5) to see the changes.")

        except Exception as e:
            error_msg = f"Error updating preview: {str(e)}"
            self.web_preview_status.set(f"❌ Error: {str(e)}")
            messagebox.showerror("Update Error", error_msg)
            logger.error(error_msg)

    def stop_web_preview(self):
        """Stop the web preview server with improved error handling"""
        try:
            self.web_preview_status.set("Stopping web preview...")
            self.web_preview_window.update()

            # Stop web server gracefully
            if self.web_server:
                try:
                    logger.info("Shutting down web server...")
                    self.web_server.shutdown()
                    self.web_server.server_close()
                    logger.info("Web server shut down successfully")
                except Exception as e:
                    logger.warning(f"Error shutting down web server: {e}")
                finally:
                    self.web_server = None

            # Wait for server thread to finish
            if self.web_server_thread and self.web_server_thread.is_alive():
                try:
                    logger.info("Waiting for server thread to finish...")
                    self.web_server_thread.join(timeout=3.0)  # Wait max 3 seconds
                    if self.web_server_thread.is_alive():
                        logger.warning("Server thread did not finish gracefully")
                    else:
                        logger.info("Server thread finished successfully")
                except Exception as e:
                    logger.warning(f"Error waiting for server thread: {e}")
                finally:
                    self.web_server_thread = None

            # Clean up temporary files
            if self.temp_preview_dir and os.path.exists(self.temp_preview_dir):
                try:
                    logger.info(f"Cleaning up temp directory: {self.temp_preview_dir}")
                    # Change back to original directory first
                    os.chdir(os.path.dirname(os.path.abspath(__file__)))
                    # Remove temp directory
                    shutil.rmtree(self.temp_preview_dir)
                    logger.info("Temp directory cleaned up successfully")
                except Exception as e:
                    logger.warning(f"Error cleaning up temp directory: {e}")
                    # Try alternative cleanup
                    try:
                        import time
                        time.sleep(0.5)  # Brief delay
                        shutil.rmtree(self.temp_preview_dir, ignore_errors=True)
                    except:
                        pass
                finally:
                    self.temp_preview_dir = None

            self.web_preview_status.set("🛑 Web preview stopped successfully")

            # Update info
            self.show_web_preview_info()

            logger.info("Web preview stopped successfully")
            messagebox.showinfo("Preview Stopped",
                              "Web preview has been stopped and temporary files cleaned up.\n\n"
                              "You can now start a new preview or close this window.")

        except Exception as e:
            error_msg = f"Error stopping preview: {str(e)}"
            self.web_preview_status.set(f"❌ Error stopping: {str(e)}")
            logger.error(error_msg)

            # Force cleanup
            try:
                self.web_server = None
                self.web_server_thread = None
                if self.temp_preview_dir:
                    shutil.rmtree(self.temp_preview_dir, ignore_errors=True)
                    self.temp_preview_dir = None
            except:
                pass

    def get_server_port(self):
        """Get the current server port"""
        if self.web_server:
            return self.web_server.server_address[1]
        return None

    def show_web_preview_info(self):
        """Show initial web preview information"""
        config = self.get_client_config()

        info_text = f"""
WEB PREVIEW INFORMATION
{'=' * 50}

Current Configuration:
• Business Name: {config.business_name or 'Not set'}
• Dashboard Title: {config.dashboard_title or 'Default'}
• Primary Color: {config.primary_color}
• Secondary Color: {config.secondary_color}
• Font Family: {config.font_family}
• Single Location Mode: {'Yes' if config.single_location_mode else 'No'}
• Locations: {', '.join(config.locations) if config.locations else 'Default'}

Web Preview Process:
1. Click 'Start Web Preview' to begin
2. Temporary files will be created with your customizations
3. Local web server will start
4. Real dashboard will open in your browser
5. Click 'Update Preview' to apply new changes
6. Click 'Stop Preview' when finished

Status: Ready to start web preview
        """.strip()

        self.web_preview_info.delete('1.0', tk.END)
        self.web_preview_info.insert('1.0', info_text)

    def update_web_preview_info(self, config: ClientConfig, port: int):
        """Update web preview info after starting"""
        info_text = f"""
WEB PREVIEW - RUNNING
{'=' * 50}

Server Information:
• URL: http://localhost:{port}
• Status: Running
• Temp Directory: {self.temp_preview_dir}

Applied Customizations:
• Business Name: "{config.business_name}" → Replaced throughout dashboard
• Dashboard Title: "{config.dashboard_title or config.business_name + ' Attribution Dashboard'}"
• Primary Color: {config.primary_color} → Applied to headers and buttons
• Secondary Color: {config.secondary_color} → Applied to hover effects
• Font Family: {config.font_family} → Applied throughout

Header Changes:
• Logo alt text: "{config.business_name} Attribution Logo"
• Business name display added next to logo
• Styled with your chosen colors and fonts

Location Settings:
• Single Location Mode: {'Enabled' if config.single_location_mode else 'Disabled'}
"""

        if config.single_location_mode:
            location_name = config.locations[0] if config.locations else "Main Location"
            info_text += f"""• All filters hidden for clean interface
• Location badge shows: "{location_name}"
• Location-specific charts disabled
"""
        else:
            info_text += f"""• Multi-location filters visible
• Location options: {', '.join(config.locations) if config.locations else 'Default locations'}
• All location features enabled
"""

        if config.locations:
            info_text += f"""
Location Name Replacements:
"""
            default_locations = ['Daphne', 'Mobile', 'Foley']
            for i, location in enumerate(default_locations):
                if i < len(config.locations):
                    info_text += f"• 'Quick Fix - {location}' → '{config.business_name} - {config.locations[i]}'\n"

        info_text += f"""
Files Modified (in temp directory):
• index.html - Business name, logo, location filters
• styles.css - Colors, fonts, custom styles
• script.js - Location names, filtering logic

Instructions:
• The REAL dashboard is now running with your customizations
• Make changes in the main customization tab
• Click 'Update Preview' to apply new changes
• Refresh your browser (F5) to see updates
• Click 'Stop Preview' when finished

Status: ✅ Web preview running - showing REAL dashboard with your customizations!
        """

        self.web_preview_info.delete('1.0', tk.END)
        self.web_preview_info.insert('1.0', info_text)

    def on_web_preview_close(self):
        """Handle web preview window close with proper cleanup"""
        try:
            # Check if server is running
            server_running = self.web_server is not None

            if server_running:
                response = messagebox.askyesnocancel(
                    "Close Web Preview",
                    "Web preview server is still running.\n\n"
                    "Do you want to stop the server and close the window?\n\n"
                    "• Yes: Stop server and close\n"
                    "• No: Close window only (server keeps running)\n"
                    "• Cancel: Keep window open"
                )

                if response is None:  # Cancel
                    return
                elif response:  # Yes - stop server and close
                    self.stop_web_preview()
                    self.web_preview_window.destroy()
                else:  # No - close window only
                    messagebox.showwarning(
                        "Server Still Running",
                        "The web preview server is still running in the background.\n\n"
                        "You can access it at the same URL until you restart the application."
                    )
                    self.web_preview_window.destroy()
            else:
                # No server running, just close
                self.web_preview_window.destroy()

        except Exception as e:
            logger.error(f"Error closing web preview window: {e}")
            # Force close
            try:
                self.web_preview_window.destroy()
            except:
                pass

    def cleanup_web_preview_on_exit(self):
        """Cleanup web preview resources when application exits"""
        try:
            if hasattr(self, 'web_server') and self.web_server:
                logger.info("Cleaning up web server on application exit...")
                self.web_server.shutdown()
                self.web_server.server_close()

            if hasattr(self, 'temp_preview_dir') and self.temp_preview_dir:
                logger.info("Cleaning up temp directory on application exit...")
                shutil.rmtree(self.temp_preview_dir, ignore_errors=True)

        except Exception as e:
            logger.warning(f"Error during web preview cleanup on exit: {e}")

    def find_free_port(self):
        """Find a free port for the web server with better error handling"""
        try:
            with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as s:
                s.bind(('', 0))
                s.listen(1)
                port = s.getsockname()[1]
                # Verify port is actually free
                s.close()
                return port
        except Exception as e:
            logger.error(f"Error finding free port: {e}")
            # Fallback to common ports
            for port in range(8000, 8100):
                try:
                    with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as s:
                        s.bind(('', port))
                        return port
                except:
                    continue
            raise Exception("No free ports available")

    def create_html_preview_area(self, parent):
        """Create the HTML preview display area"""
        # Create a frame for the HTML preview
        html_frame = ttk.Frame(parent)
        html_frame.pack(fill='both', expand=True)

        # Try different approaches for HTML rendering
        if WEBVIEW_AVAILABLE:
            # Use webview for better HTML rendering
            self.create_webview_preview(html_frame)
        else:
            # Fallback to text preview with browser option
            self.create_fallback_preview(html_frame)

    def create_webview_preview(self, parent):
        """Create webview-based HTML preview"""
        try:
            # Create a placeholder for webview
            self.webview_frame = ttk.Frame(parent)
            self.webview_frame.pack(fill='both', expand=True)

            # Instructions for webview
            info_label = ttk.Label(self.webview_frame,
                                  text="🌐 Live preview will open in a separate browser window\nClick 'Update Preview' to refresh",
                                  font=('Arial', 12), justify='center')
            info_label.pack(expand=True)

            self.webview_available = True

        except Exception as e:
            logger.warning(f"Webview not available: {e}")
            self.create_fallback_preview(parent)

    def create_fallback_preview(self, parent):
        """Create fallback HTML preview using text display"""
        # Create notebook for different preview types
        preview_notebook = ttk.Notebook(parent)
        preview_notebook.pack(fill='both', expand=True)

        # Visual Preview Tab (using browser)
        visual_frame = ttk.Frame(preview_notebook)
        preview_notebook.add(visual_frame, text="🖥️ Visual Preview")

        # Instructions and browser button
        instruction_frame = ttk.Frame(visual_frame)
        instruction_frame.pack(fill='x', pady=10)

        ttk.Label(instruction_frame,
                 text="Live visual preview opens in your default browser:",
                 font=('Arial', 11)).pack()

        browser_frame = ttk.Frame(instruction_frame)
        browser_frame.pack(pady=10)

        ttk.Button(browser_frame, text="🌐 Open Live Preview in Browser",
                  command=self.open_preview_in_browser,
                  style='Accent.TButton').pack()

        # Preview status
        self.preview_status_var = tk.StringVar(value="Ready to preview...")
        ttk.Label(instruction_frame, textvariable=self.preview_status_var,
                 font=('Arial', 9), foreground='gray').pack(pady=5)

        # HTML Source Tab
        source_frame = ttk.Frame(preview_notebook)
        preview_notebook.add(source_frame, text="📄 HTML Source")

        self.live_html_text = scrolledtext.ScrolledText(source_frame, wrap='word', font=('Consolas', 9))
        self.live_html_text.pack(fill='both', expand=True)

        # CSS Preview Tab
        css_frame = ttk.Frame(preview_notebook)
        preview_notebook.add(css_frame, text="🎨 CSS Styles")

        self.live_css_text = scrolledtext.ScrolledText(css_frame, wrap='word', font=('Consolas', 9))
        self.live_css_text.pack(fill='both', expand=True)

        self.webview_available = False

    def update_live_preview(self, preview_frame):
        """Update the live preview with current settings"""
        try:
            config = self.get_client_config()

            if not config.business_name:
                config.business_name = "Your Business Name"

            # Generate preview HTML based on mode
            mode = self.preview_mode_var.get()

            if mode == "header":
                html_content = self.generate_header_preview(config)
            elif mode == "full_page":
                html_content = self.generate_full_page_preview(config)
            else:  # dashboard_only
                html_content = self.generate_dashboard_preview(config)

            # Update HTML source display
            if hasattr(self, 'live_html_text'):
                self.live_html_text.delete('1.0', tk.END)
                self.live_html_text.insert('1.0', html_content)

            # Update CSS display
            if hasattr(self, 'live_css_text'):
                css_content = self.generate_preview_css(config)
                self.live_css_text.delete('1.0', tk.END)
                self.live_css_text.insert('1.0', css_content)

            # Save to temporary file for browser preview
            self.current_preview_html = html_content
            self.save_temp_preview(html_content)

            # Update status
            if hasattr(self, 'preview_status_var'):
                self.preview_status_var.set(f"Preview updated - {mode.replace('_', ' ').title()} Mode")

        except Exception as e:
            error_msg = f"Error updating live preview: {str(e)}"
            logger.error(error_msg)
            if hasattr(self, 'preview_status_var'):
                self.preview_status_var.set(f"Error: {str(e)}")

    def generate_header_preview(self, config: ClientConfig) -> str:
        """Generate header-only preview HTML"""
        single_location_badge = ""
        filter_section = ""

        if config.single_location_mode:
            location_name = config.locations[0] if config.locations else "Main Location"
            single_location_badge = f'<div class="location-badge">{location_name}</div>'
        else:
            # Show filter controls for multi-location
            location_options = ""
            if config.locations:
                location_options = "\n".join([
                    f'                            <option value="{loc}">{loc}</option>'
                    for loc in config.locations
                ])

            filter_section = f'''
                <div class="filter-container">
                    <div class="filter-group">
                        <label for="date-filter">Date:</label>
                        <select id="date-filter" class="filter-select">
                            <option value="all">All Dates</option>
                            <option value="today">Today</option>
                            <option value="week">This Week</option>
                            <option value="month">This Month</option>
                        </select>
                    </div>
                    <div class="filter-group">
                        <label for="location-filter">Location:</label>
                        <select id="location-filter" class="filter-select">
                            <option value="all">All Locations</option>
                            {location_options}
                        </select>
                    </div>
                    <button class="btn custom-range-btn">Custom Range</button>
                </div>
            '''

        return f'''
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{config.dashboard_title or config.business_name + " Attribution Dashboard"}</title>
    <style>
        {self.generate_preview_css(config)}
    </style>
</head>
<body>
    <div class="container">
        <header>
            <div class="header-content">
                <div>
                    <div class="logo">
                        <img src="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNDAiIGhlaWdodD0iNDAiIHZpZXdCb3g9IjAgMCA0MCA0MCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHJlY3Qgd2lkdGg9IjQwIiBoZWlnaHQ9IjQwIiByeD0iOCIgZmlsbD0iIzMzNzNkYyIvPgo8dGV4dCB4PSIyMCIgeT0iMjYiIGZvbnQtZmFtaWx5PSJBcmlhbCIgZm9udC1zaXplPSIxOCIgZm9udC13ZWlnaHQ9ImJvbGQiIGZpbGw9IndoaXRlIiB0ZXh0LWFuY2hvcj0ibWlkZGxlIj5MPC90ZXh0Pgo8L3N2Zz4K"
                             alt="{config.business_name} Attribution Logo" />
                    </div>
                    <div class="business-name-display">
                        <h2 class="business-name">{config.business_name}</h2>
                        <p class="business-subtitle">Attribution Dashboard</p>
                    </div>
                </div>
                <div class="header-right">
                    {single_location_badge}
                    {filter_section}
                </div>
            </div>
        </header>

        <div class="preview-info">
            <h3>🎨 Header Preview</h3>
            <p>This is how your dashboard header will look with the current settings.</p>
            <div class="config-summary">
                <strong>Business:</strong> {config.business_name}<br>
                <strong>Mode:</strong> {"Single Location" if config.single_location_mode else "Multi Location"}<br>
                <strong>Primary Color:</strong> {config.primary_color}<br>
                <strong>Font:</strong> {config.font_family}
            </div>
        </div>
    </div>
</body>
</html>
        '''

    def generate_preview_css(self, config: ClientConfig) -> str:
        """Generate CSS for preview"""
        return f'''
        * {{
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }}

        body {{
            font-family: {config.font_family}, sans-serif;
            background: #f5f7fa;
            color: #333;
            line-height: 1.6;
        }}

        .container {{
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }}

        header {{
            background: white;
            border-radius: 12px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            margin-bottom: 20px;
        }}

        .header-content {{
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 20px 30px;
        }}

        .header-content > div:first-child {{
            display: flex;
            align-items: center;
            gap: 20px;
        }}

        .logo img {{
            height: 40px;
            width: 40px;
        }}

        .business-name-display {{
            display: flex;
            flex-direction: column;
        }}

        .business-name {{
            font-size: 1.8rem;
            font-weight: 700;
            color: {config.primary_color};
            margin: 0;
            line-height: 1.2;
        }}

        .business-subtitle {{
            font-size: 0.9rem;
            color: #666;
            font-weight: 500;
            margin: 0;
        }}

        .header-right {{
            display: flex;
            align-items: center;
            gap: 20px;
        }}

        .location-badge {{
            background: {config.primary_color};
            color: white;
            padding: 8px 16px;
            border-radius: 20px;
            font-size: 0.9rem;
            font-weight: 600;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }}

        .filter-container {{
            display: flex;
            gap: 15px;
            align-items: center;
        }}

        .filter-group {{
            display: flex;
            flex-direction: column;
            gap: 5px;
        }}

        .filter-group label {{
            font-size: 0.9rem;
            font-weight: 500;
            color: #333;
        }}

        .filter-select {{
            padding: 8px 12px;
            border: 1px solid #ddd;
            border-radius: 6px;
            background: white;
            font-size: 0.9rem;
            min-width: 120px;
        }}

        .btn {{
            padding: 8px 16px;
            background: {config.primary_color};
            color: white;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            font-size: 0.9rem;
            font-weight: 500;
            transition: background-color 0.2s;
        }}

        .btn:hover {{
            background: {config.secondary_color};
        }}

        .preview-info {{
            background: white;
            padding: 20px;
            border-radius: 12px;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }}

        .preview-info h3 {{
            color: {config.primary_color};
            margin-bottom: 10px;
        }}

        .config-summary {{
            background: #f8f9fa;
            padding: 15px;
            border-radius: 8px;
            margin-top: 15px;
            border-left: 4px solid {config.primary_color};
        }}

        /* Single location mode styles */
        .single-location-header {{
            display: flex;
            align-items: center;
            gap: 15px;
        }}
        '''

    def save_temp_preview(self, html_content: str):
        """Save preview HTML to temporary file"""
        try:
            # Create temp file
            with tempfile.NamedTemporaryFile(mode='w', suffix='.html', delete=False, encoding='utf-8') as f:
                f.write(html_content)
                self.temp_preview_file = f.name
        except Exception as e:
            logger.error(f"Error saving temp preview: {e}")

    def open_preview_in_browser(self):
        """Open the current preview in browser"""
        if hasattr(self, 'current_preview_html'):
            self.save_temp_preview(self.current_preview_html)
            webbrowser.open(f'file://{self.temp_preview_file}')
            if hasattr(self, 'preview_status_var'):
                self.preview_status_var.set("Preview opened in browser")
        else:
            messagebox.showwarning("Warning", "No preview available. Please update preview first.")

    def setup_auto_update(self, preview_frame):
        """Setup auto-update for live preview"""
        def auto_update_loop():
            while hasattr(self, 'live_preview_window') and self.live_preview_window.winfo_exists():
                if self.auto_update_var.get():
                    try:
                        self.update_live_preview(preview_frame)
                    except Exception as e:
                        logger.error(f"Auto-update error: {e}")
                time.sleep(2)  # Update every 2 seconds

        # Start auto-update in background thread
        if hasattr(self, 'auto_update_var'):
            update_thread = threading.Thread(target=auto_update_loop, daemon=True)
            update_thread.start()

    def generate_full_page_preview(self, config: ClientConfig) -> str:
        """Generate full page preview (simplified dashboard)"""
        header_html = self.generate_header_preview(config)

        # Extract just the header part and add dashboard content
        header_section = re.search(r'<header>.*?</header>', header_html, re.DOTALL)
        header_content = header_section.group(0) if header_section else ""

        return f'''
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{config.dashboard_title or config.business_name + " Attribution Dashboard"}</title>
    <style>
        {self.generate_preview_css(config)}

        .dashboard-content {{
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-top: 20px;
        }}

        .stat-card {{
            background: white;
            padding: 20px;
            border-radius: 12px;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
            text-align: center;
        }}

        .stat-value {{
            font-size: 2rem;
            font-weight: bold;
            color: {config.primary_color};
            margin-bottom: 5px;
        }}

        .stat-label {{
            color: #666;
            font-size: 0.9rem;
        }}

        .chart-placeholder {{
            background: #f8f9fa;
            height: 200px;
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #666;
            margin: 20px 0;
        }}
    </style>
</head>
<body>
    <div class="container">
        {header_content}

        <div class="dashboard-content">
            <div class="stat-card">
                <div class="stat-value">$12,450</div>
                <div class="stat-label">Total Revenue</div>
            </div>
            <div class="stat-card">
                <div class="stat-value">156</div>
                <div class="stat-label">Total Transactions</div>
            </div>
            <div class="stat-card">
                <div class="stat-value">$79.81</div>
                <div class="stat-label">Average Order Value</div>
            </div>
            <div class="stat-card">
                <div class="stat-value">4.2%</div>
                <div class="stat-label">Conversion Rate</div>
            </div>
        </div>

        <div class="chart-placeholder">
            📊 Revenue Chart Placeholder
        </div>

        <div class="preview-info">
            <h3>📊 Full Dashboard Preview</h3>
            <p>This shows how your complete dashboard will look with sample data.</p>
        </div>
    </div>
</body>
</html>
        '''

    def generate_dashboard_preview(self, config: ClientConfig) -> str:
        """Generate dashboard-only preview (no header)"""
        return f'''
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Dashboard Content Preview</title>
    <style>
        {self.generate_preview_css(config)}

        .dashboard-grid {{
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 15px;
        }}

        .metric-card {{
            background: white;
            padding: 15px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
            border-left: 4px solid {config.primary_color};
        }}

        .metric-title {{
            font-size: 0.9rem;
            color: #666;
            margin-bottom: 8px;
        }}

        .metric-value {{
            font-size: 1.5rem;
            font-weight: bold;
            color: {config.primary_color};
        }}
    </style>
</head>
<body>
    <div class="container">
        <h2 style="color: {config.primary_color}; margin-bottom: 20px;">
            {config.business_name} - Dashboard Content
        </h2>

        <div class="dashboard-grid">
            <div class="metric-card">
                <div class="metric-title">Google Ads Revenue</div>
                <div class="metric-value">$8,230</div>
            </div>
            <div class="metric-card">
                <div class="metric-title">Meta Ads Revenue</div>
                <div class="metric-value">$4,220</div>
            </div>
            <div class="metric-card">
                <div class="metric-title">Total Clicks</div>
                <div class="metric-value">1,245</div>
            </div>
            <div class="metric-card">
                <div class="metric-title">Cost Per Click</div>
                <div class="metric-value">$2.45</div>
            </div>
        </div>

        <div class="preview-info" style="margin-top: 20px;">
            <h3>📈 Dashboard Content Preview</h3>
            <p>This shows the main dashboard content area with your branding applied.</p>
        </div>
    </div>
</body>
</html>
        '''
    
    def generate_dashboard(self):
        """Generate the customized dashboard"""
        if not self.source_directory:
            messagebox.showerror("Error", "Please select a source directory first.")
            return
        
        if not self.output_dir_var.get():
            messagebox.showerror("Error", "Please select an output directory.")
            return
        
        config = self.get_client_config()
        
        if not config.business_name:
            messagebox.showerror("Error", "Please enter a business name.")
            return
        
        # Confirm generation
        if not messagebox.askyesno("Generate Dashboard", 
                                  f"Generate customized dashboard for {config.business_name}?\n\n"
                                  f"Source: {self.source_directory}\n"
                                  f"Output: {self.output_dir_var.get()}"):
            return
        
        # Start generation process
        self.progress_var.set("Starting generation...")
        self.progress_bar['value'] = 0
        self.root.update()
        
        try:
            # Clear previous results
            self.generation_results.delete('1.0', tk.END)
            
            # Step 1: Validate source
            self.progress_var.set("Validating source directory...")
            self.progress_bar['value'] = 10
            self.root.update()
            
            if not self.analysis_results:
                self.analyze_codebase()
            
            # Step 2: Create backup if requested
            if self.backup_original_var.get():
                self.progress_var.set("Creating backup...")
                self.progress_bar['value'] = 20
                self.root.update()
                
                backup_dir = self.output_dir_var.get() + "_backup_" + datetime.now().strftime("%Y%m%d_%H%M%S")
                shutil.copytree(self.source_directory, backup_dir)
                self.log_generation(f"✅ Backup created: {backup_dir}")
            
            # Step 3: Generate customized dashboard
            self.progress_var.set("Generating customized dashboard...")
            self.progress_bar['value'] = 40
            self.root.update()
            
            success = self.customizer.customize_dashboard(
                self.source_directory,
                self.output_dir_var.get(),
                config
            )
            
            if not success:
                raise Exception("Dashboard customization failed")
            
            self.log_generation(f"✅ Dashboard customized successfully")
            
            # Step 4: Validate output if requested
            if self.validate_output_var.get():
                self.progress_var.set("Validating generated files...")
                self.progress_bar['value'] = 70
                self.root.update()
                
                self.validate_generated_files(self.output_dir_var.get())
            
            # Step 5: Generate README if requested
            if self.generate_readme_var.get():
                self.progress_var.set("Generating documentation...")
                self.progress_bar['value'] = 85
                self.root.update()
                
                self.generate_readme_file(self.output_dir_var.get(), config)
            
            # Step 6: Complete
            self.progress_var.set("Generation complete!")
            self.progress_bar['value'] = 100
            self.root.update()
            
            self.log_generation(f"🎉 Dashboard generation completed successfully!")
            self.log_generation(f"📁 Output directory: {self.output_dir_var.get()}")
            
            # Show completion message
            messagebox.showinfo("Success", 
                              f"Dashboard generated successfully!\n\n"
                              f"Output: {self.output_dir_var.get()}\n"
                              f"Business: {config.business_name}")
            
            self.status_var.set(f"Dashboard generated for {config.business_name}")
            
        except Exception as e:
            error_msg = f"Error generating dashboard: {str(e)}"
            self.log_generation(f"❌ {error_msg}")
            messagebox.showerror("Generation Error", error_msg)
            self.progress_var.set("Generation failed")
            logger.error(error_msg)
    
    def preview_generation(self):
        """Preview what will be generated without actually creating files"""
        if not self.source_directory:
            messagebox.showerror("Error", "Please select a source directory first.")
            return
        
        config = self.get_client_config()
        
        # Create preview window
        preview_window = tk.Toplevel(self.root)
        preview_window.title("Generation Preview")
        preview_window.geometry("900x700")
        
        # Create notebook for different preview tabs
        preview_notebook = ttk.Notebook(preview_window)
        preview_notebook.pack(fill='both', expand=True, padx=10, pady=10)
        
        # HTML Preview
        html_frame = ttk.Frame(preview_notebook)
        preview_notebook.add(html_frame, text="HTML Changes")
        
        html_text = scrolledtext.ScrolledText(html_frame, wrap='word', font=('Consolas', 9))
        html_text.pack(fill='both', expand=True)
        
        # CSS Preview
        css_frame = ttk.Frame(preview_notebook)
        preview_notebook.add(css_frame, text="CSS Changes")
        
        css_text = scrolledtext.ScrolledText(css_frame, wrap='word', font=('Consolas', 9))
        css_text.pack(fill='both', expand=True)
        
        # JavaScript Preview
        js_frame = ttk.Frame(preview_notebook)
        preview_notebook.add(js_frame, text="JavaScript Changes")
        
        js_text = scrolledtext.ScrolledText(js_frame, wrap='word', font=('Consolas', 9))
        js_text.pack(fill='both', expand=True)
        
        # Generate preview content
        self.generate_preview_content(html_text, css_text, js_text, config)
        
        # Add close button
        ttk.Button(preview_window, text="Close", command=preview_window.destroy).pack(pady=10)
    
    def generate_preview_content(self, html_text, css_text, js_text, config):
        """Generate preview content for different file types"""
        # HTML Preview
        html_preview = f"""
HTML MODIFICATIONS PREVIEW
{'=' * 30}

Title Changes:
• Page title: "{config.dashboard_title or config.business_name + ' Attribution Dashboard'}"
• Logo alt text: "{config.business_name} Attribution Logo"

Logo Changes:
• Logo source: {config.logo_path or 'img/rl.svg (default)'}

Business Name Replacements:
• "RepairLift" → "{config.business_name}"
• "Quick Fix" → "{config.business_name}"

Location Filter Changes:
"""
        
        if config.single_location_mode:
            html_preview += "• Location filter dropdown will be HIDDEN\n"
            html_preview += "• Location-specific cards will be HIDDEN\n"
        else:
            html_preview += "• Location filter dropdown will remain visible\n"
            if config.locations:
                html_preview += f"• Location options: {', '.join(config.locations)}\n"
        
        html_text.insert('1.0', html_preview)
        
        # CSS Preview
        css_preview = f"""
CSS MODIFICATIONS PREVIEW
{'=' * 30}

Color Variables:
• --primary: {config.primary_color}
• --secondary: {config.secondary_color}

Font Family:
• Primary font: "{config.font_family}", sans-serif

Custom CSS:
{config.custom_css or 'No custom CSS specified'}

Single Location Mode Styles:
"""
        
        if config.single_location_mode:
            css_preview += """
.location-filter, .location-breakdown, .location-stat {
    display: none !important;
}
"""
        else:
            css_preview += "No single-location mode styles will be added"
        
        css_text.insert('1.0', css_preview)
        
        # JavaScript Preview
        js_preview = f"""
JAVASCRIPT MODIFICATIONS PREVIEW
{'=' * 30}

Location Name Replacements:
"""
        
        if config.locations:
            default_locations = ['Daphne', 'Mobile', 'Foley']
            for i, location in enumerate(default_locations):
                if i < len(config.locations):
                    js_preview += f"• '{location}' → '{config.locations[i]}'\n"
                    js_preview += f"• 'Quick Fix - {location}' → '{config.business_name} - {config.locations[i]}'\n"
        
        if config.single_location_mode:
            single_location = config.locations[0] if config.locations else "Main"
            js_preview += f"""
Single Location Mode Changes:
• currentLocationFilter set to: '{single_location}'
• Location filter dropdown will be hidden
• Location filtering events will be disabled
"""
        
        js_text.insert('1.0', js_preview)
    
    def validate_generated_files(self, output_dir):
        """Validate the generated files"""
        validation_results = []
        
        for filename in self.analyzer.required_files:
            file_path = os.path.join(output_dir, filename)
            if os.path.exists(file_path):
                validation_results.append(f"✅ {filename} - Generated successfully")
            else:
                validation_results.append(f"❌ {filename} - Missing")
        
        # Check for client config file
        client_config_path = os.path.join(output_dir, 'client_config.json')
        if os.path.exists(client_config_path):
            validation_results.append("✅ client_config.json - Generated successfully")
        
        for result in validation_results:
            self.log_generation(result)
    
    def generate_readme_file(self, output_dir, config):
        """Generate README file with customization details"""
        readme_content = f"""# {config.business_name} Attribution Dashboard

This is a customized version of the Attribution Dashboard specifically configured for {config.business_name}.

## Customization Details

### Business Information
- **Business Name**: {config.business_name}
- **Dashboard Title**: {config.dashboard_title}
- **Contact Email**: {config.contact_email}

### Branding
- **Primary Color**: {config.primary_color}
- **Secondary Color**: {config.secondary_color}
- **Font Family**: {config.font_family}
- **Logo**: {config.logo_path or 'Default logo'}

### Location Configuration
- **Single Location Mode**: {'Yes' if config.single_location_mode else 'No'}
- **Location Filtering Enabled**: {'Yes' if config.enable_location_filtering else 'No'}
- **Locations**: {', '.join(config.locations) if config.locations else 'Default locations'}

### Airtable Configuration
- **Base ID**: {config.airtable_base_id or 'Not configured'}

## Installation

1. Install Python dependencies:
   ```bash
   pip install -r requirements.txt
   ```

2. Configure environment variables:
   - Set your Airtable API key
   - Update the base ID in config.py if needed

3. Run the dashboard:
   ```bash
   python server.py
   ```

## Customization Notes

This dashboard has been customized using the Dashboard Customization Tool v1.0.

### Files Modified:
- `index.html` - Business name, logo, location filters
- `styles.css` - Colors, fonts, custom styles
- `script.js` - Location names, filtering logic
- `config.py` - Airtable configuration, client settings

### Custom CSS Applied:
```css
{config.custom_css or '/* No custom CSS */'}
```

## Support

For support or questions about this customized dashboard, contact: {config.contact_email}

Generated on: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
"""

        readme_path = os.path.join(output_dir, 'README.md')
        with open(readme_path, 'w', encoding='utf-8') as f:
            f.write(readme_content)

        self.log_generation(f"✅ README.md generated")

    def log_generation(self, message):
        """Log a message to the generation results"""
        timestamp = datetime.now().strftime('%H:%M:%S')
        log_message = f"[{timestamp}] {message}\n"
        self.generation_results.insert(tk.END, log_message)
        self.generation_results.see(tk.END)
        self.root.update()

    def create_generation_tab(self):
        """Create the code generation tab"""
        self.generation_frame = ttk.Frame(self.notebook)
        self.notebook.add(self.generation_frame, text="🚀 Generate Dashboard")

        # Output directory selection
        output_frame = ttk.LabelFrame(self.generation_frame, text="Output Configuration", padding=10)
        output_frame.pack(fill='x', padx=10, pady=5)

        ttk.Label(output_frame, text="Output Directory:").pack(anchor='w')

        output_dir_frame = ttk.Frame(output_frame)
        output_dir_frame.pack(fill='x', pady=5)

        self.output_dir_var = tk.StringVar()
        ttk.Entry(output_dir_frame, textvariable=self.output_dir_var, width=80).pack(side='left', fill='x', expand=True)
        ttk.Button(output_dir_frame, text="Browse", command=self.browse_output_directory).pack(side='right', padx=(5, 0))

        # Generation options
        options_frame = ttk.LabelFrame(self.generation_frame, text="Generation Options", padding=10)
        options_frame.pack(fill='x', padx=10, pady=5)

        self.backup_original_var = tk.BooleanVar(value=True)
        ttk.Checkbutton(options_frame, text="Create backup of original files",
                       variable=self.backup_original_var).pack(anchor='w')

        self.validate_output_var = tk.BooleanVar(value=True)
        ttk.Checkbutton(options_frame, text="Validate generated files",
                       variable=self.validate_output_var).pack(anchor='w')

        self.generate_readme_var = tk.BooleanVar(value=True)
        ttk.Checkbutton(options_frame, text="Generate README with customization details",
                       variable=self.generate_readme_var).pack(anchor='w')

        # Generation controls
        controls_frame = ttk.Frame(options_frame)
        controls_frame.pack(fill='x', pady=10)

        ttk.Button(controls_frame, text="🎯 Generate Customized Dashboard",
                  command=self.generate_dashboard).pack(side='left')
        ttk.Button(controls_frame, text="🔍 Preview Changes Only",
                  command=self.preview_generation).pack(side='left', padx=(10, 0))
        ttk.Button(controls_frame, text="⚡ Generate Optimized Client Instance",
                  command=self.generate_client_instance).pack(side='left', padx=(10, 0))

        # Progress and status
        progress_frame = ttk.LabelFrame(self.generation_frame, text="Generation Progress", padding=10)
        progress_frame.pack(fill='x', padx=10, pady=5)

        self.progress_var = tk.StringVar(value="Ready to generate...")
        ttk.Label(progress_frame, textvariable=self.progress_var).pack(anchor='w')

        self.progress_bar = ttk.Progressbar(progress_frame, mode='determinate')
        self.progress_bar.pack(fill='x', pady=5)

        # Generation results
        results_frame = ttk.LabelFrame(self.generation_frame, text="Generation Results", padding=10)
        results_frame.pack(fill='both', expand=True, padx=10, pady=5)

        self.generation_results = scrolledtext.ScrolledText(results_frame, height=15, wrap='word')
        self.generation_results.pack(fill='both', expand=True)

        # Action buttons
        action_frame = ttk.Frame(results_frame)
        action_frame.pack(fill='x', pady=(10, 0))

        ttk.Button(action_frame, text="📂 Open Output Directory",
                  command=self.open_output_directory).pack(side='left')
        ttk.Button(action_frame, text="🌐 Test Generated Dashboard",
                  command=self.test_dashboard).pack(side='left', padx=(5, 0))
        ttk.Button(action_frame, text="📋 Copy Generation Report",
                  command=self.copy_generation_report).pack(side='right')

    def create_logs_tab(self):
        """Create the logs and debugging tab"""
        self.logs_frame = ttk.Frame(self.notebook)
        self.notebook.add(self.logs_frame, text="📋 Logs & Debug")

        # Log controls
        controls_frame = ttk.Frame(self.logs_frame)
        controls_frame.pack(fill='x', padx=10, pady=5)

        ttk.Button(controls_frame, text="🔄 Refresh Logs", command=self.refresh_logs).pack(side='left')
        ttk.Button(controls_frame, text="🗑️ Clear Logs", command=self.clear_logs).pack(side='left', padx=(5, 0))
        ttk.Button(controls_frame, text="💾 Save Logs", command=self.save_logs).pack(side='left', padx=(5, 0))

        # Log level selection
        ttk.Label(controls_frame, text="Log Level:").pack(side='right', padx=(0, 5))
        self.log_level_var = tk.StringVar(value="INFO")
        log_level_combo = ttk.Combobox(controls_frame, textvariable=self.log_level_var,
                                      values=["DEBUG", "INFO", "WARNING", "ERROR"], width=10)
        log_level_combo.pack(side='right')
        log_level_combo.bind('<<ComboboxSelected>>', self.change_log_level)

        # Log display
        log_frame = ttk.LabelFrame(self.logs_frame, text="Application Logs", padding=10)
        log_frame.pack(fill='both', expand=True, padx=10, pady=5)

        self.log_text = scrolledtext.ScrolledText(log_frame, height=20, wrap='word', font=('Consolas', 9))
        self.log_text.pack(fill='both', expand=True)

        # Debug information
        debug_frame = ttk.LabelFrame(self.logs_frame, text="Debug Information", padding=10)
        debug_frame.pack(fill='x', padx=10, pady=5)

        debug_info = f"""
Application Version: 1.0.0
Python Version: {sys.version}
Platform: {sys.platform}
Working Directory: {os.getcwd()}
Log File: dashboard_customizer.log
        """.strip()

        ttk.Label(debug_frame, text=debug_info, font=('Consolas', 9)).pack(anchor='w')

    def setup_layout(self):
        """Setup the main layout"""
        self.notebook.pack(fill='both', expand=True, padx=5, pady=5)

        # Status bar
        self.status_frame = ttk.Frame(self.root)
        self.status_frame.pack(fill='x', side='bottom')

        self.status_var = tk.StringVar(value="Ready - Select source directory to begin analysis")
        ttk.Label(self.status_frame, textvariable=self.status_var).pack(side='left', padx=5, pady=2)

        # Version info
        ttk.Label(self.status_frame, text="Dashboard Customizer v1.0",
                 font=('Arial', 8)).pack(side='right', padx=5, pady=2)

    def load_default_config(self):
        """Load default configuration values"""
        self.business_name_var.set("Your Business Name")
        self.dashboard_title_var.set("Attribution Dashboard")
        self.contact_email_var.set("<EMAIL>")
        self.primary_color_var.set("#e91e63")
        self.secondary_color_var.set("#ff5722")
        self.font_family_var.set("Inter")
        self.update_color_preview()

    # Event handlers and utility methods
    def browse_source_directory(self):
        """Browse for source directory"""
        directory = filedialog.askdirectory(title="Select Attribution Dashboard Source Directory")
        if directory:
            self.source_dir_var.set(directory)
            self.source_directory = directory
            self.status_var.set(f"Source directory selected: {directory}")

    def generate_client_instance(self):
        """Generate an optimized client instance with only enabled features"""
        try:
            # Validate configuration
            config = self.get_client_config()

            if not config.business_name:
                messagebox.showwarning("Warning", "Please enter a business name before generating client instance")
                return

            if not config.enabled_data_sources:
                messagebox.showwarning("Warning", "Please enable at least one data source")
                return

            # Get output directory
            output_dir = self.output_dir_var.get().strip()
            if not output_dir:
                output_dir = filedialog.askdirectory(
                    title="Select Output Directory for Client Instance",
                    initialdir=os.getcwd()
                )
                if not output_dir:
                    return
                self.output_dir_var.set(output_dir)

            # Create client-specific subdirectory
            client_dir_name = config.business_name.lower().replace(' ', '-').replace('_', '-')
            client_dir_name = re.sub(r'[^a-z0-9-]', '', client_dir_name)
            client_output_dir = os.path.join(output_dir, f"{client_dir_name}-attribution-dashboard")

            # Initialize progress
            self.progress_var.set("Generating optimized client instance...")
            self.progress_bar['value'] = 0
            self.generation_results.delete('1.0', tk.END)
            self.log_generation_message("🚀 Starting client instance generation...")

            # Create instance generator
            generator = ClientInstanceGenerator(base_path=".")

            # Update progress
            self.progress_bar['value'] = 10
            self.log_generation_message(f"📁 Creating output directory: {client_output_dir}")

            # Generate the client instance
            self.progress_bar['value'] = 20
            self.log_generation_message("⚡ Generating optimized files...")

            results = generator.generate_client_instance(config, client_output_dir)

            # Update progress based on results
            if results["success"]:
                self.progress_bar['value'] = 80
                self.log_generation_message("✅ Client instance generated successfully!")

                # Log created files
                self.log_generation_message(f"📄 Created {len(results['files_created'])} files:")
                for file_path in results["files_created"]:
                    self.log_generation_message(f"   ✓ {os.path.basename(file_path)}")

                # Log any errors
                if results["errors"]:
                    self.log_generation_message("⚠️ Warnings:")
                    for error in results["errors"]:
                        self.log_generation_message(f"   • {error}")

                # Final steps
                self.progress_bar['value'] = 90
                self.log_generation_message("📋 Generating deployment instructions...")

                # Create deployment instructions
                self._create_deployment_instructions(config, client_output_dir)

                self.progress_bar['value'] = 100
                self.progress_var.set("✅ Client instance generation completed!")

                # Show success message
                success_msg = f"""
Client instance generated successfully!

Location: {client_output_dir}

Features included:
• Data Sources: {', '.join(config.enabled_data_sources)}
• Dashboard Tabs: {', '.join(config.tab_config.enabled_tabs)}
• Optimized for: {config.cache_strategy} caching

Next steps:
1. Review the generated files
2. Set up environment variables
3. Deploy to Railway or your preferred platform

Would you like to open the output directory?
"""

                if messagebox.askyesno("Success", success_msg):
                    # Open the output directory
                    if os.name == 'nt':  # Windows
                        os.startfile(client_output_dir)
                    elif os.name == 'posix':  # macOS and Linux
                        os.system(f'open "{client_output_dir}"')

            else:
                self.progress_bar['value'] = 0
                self.progress_var.set("❌ Generation failed!")
                self.log_generation_message("❌ Client instance generation failed!")

                for error in results["errors"]:
                    self.log_generation_message(f"   • {error}")

                messagebox.showerror("Error", f"Failed to generate client instance:\n\n{chr(10).join(results['errors'])}")

        except Exception as e:
            self.progress_bar['value'] = 0
            self.progress_var.set("❌ Generation failed!")
            error_msg = f"Error generating client instance: {str(e)}"
            self.log_generation_message(f"❌ {error_msg}")
            messagebox.showerror("Error", error_msg)
            logger.error(error_msg, exc_info=True)

    def _create_deployment_instructions(self, config: 'ClientConfig', output_dir: str):
        """Create deployment instructions file"""
        instructions_file = os.path.join(output_dir, "DEPLOYMENT_INSTRUCTIONS.md")

        instructions = f"""# Deployment Instructions for {config.business_name}

## Quick Start

### 1. Environment Variables

Create a `.env` file with the following variables:

```bash
# Required
AIRTABLE_API_KEY=your_airtable_api_key_here
AIRTABLE_BASE_ID={config.airtable_base_id}
CLAUDE_API_KEY=your_claude_api_key_here

# Optional
FLASK_ENV=production
LOG_LEVEL=INFO
```

### 2. Railway Deployment

1. **Create Railway Project**:
   ```bash
   railway login
   railway init
   ```

2. **Set Environment Variables**:
   ```bash
   railway variables set AIRTABLE_API_KEY=your_key_here
   railway variables set AIRTABLE_BASE_ID={config.airtable_base_id}
   railway variables set CLAUDE_API_KEY=your_key_here
   ```

3. **Deploy**:
   ```bash
   railway up
   ```

### 3. Local Development

1. **Install Dependencies**:
   ```bash
   pip install -r requirements.txt
   ```

2. **Run Application**:
   ```bash
   python server.py
   ```

## Configuration Summary

- **Enabled Data Sources**: {', '.join(config.enabled_data_sources)}
- **Dashboard Tabs**: {', '.join(config.tab_config.enabled_tabs)}
- **Performance**: {config.cache_strategy} caching strategy
- **UI Mode**: {"Simplified" if config.simplified_navigation else "Full"} navigation

## Support

This instance was generated on {datetime.now().strftime("%Y-%m-%d %H:%M:%S")}.
For support, contact your system administrator.
"""

        with open(instructions_file, 'w', encoding='utf-8') as f:
            f.write(instructions)

        self.log_generation_message(f"📋 Created deployment instructions: DEPLOYMENT_INSTRUCTIONS.md")

    def browse_output_directory(self):
        """Browse for output directory"""
        directory = filedialog.askdirectory(title="Select Output Directory for Customized Dashboard")
        if directory:
            self.output_dir_var.set(directory)
            self.status_var.set(f"Output directory selected: {directory}")

    def browse_logo(self):
        """Browse for logo file"""
        file_path = filedialog.askopenfilename(
            title="Select Logo File",
            filetypes=[("Image files", "*.png *.jpg *.jpeg *.gif *.svg"), ("All files", "*.*")]
        )
        if file_path:
            self.logo_path_var.set(file_path)

    def update_color_preview(self, *args):
        """Update color preview labels"""
        try:
            self.primary_color_preview.config(bg=self.primary_color_var.get())
        except tk.TclError:
            self.primary_color_preview.config(bg='white')

        try:
            self.secondary_color_preview.config(bg=self.secondary_color_var.get())
        except tk.TclError:
            self.secondary_color_preview.config(bg='white')

    def on_single_location_change(self):
        """Handle single location mode change"""
        if self.single_location_var.get():
            self.enable_location_filtering_var.set(False)
            messagebox.showinfo("Single Location Mode",
                              "Location filtering will be disabled and location-specific UI elements will be hidden.")
        else:
            self.enable_location_filtering_var.set(True)

    def analyze_codebase(self):
        """Analyze the source codebase"""
        if not self.source_directory:
            messagebox.showerror("Error", "Please select a source directory first.")
            return

        self.status_var.set("Analyzing codebase...")
        self.root.update()

        try:
            # Clear previous results
            for item in self.analysis_tree.get_children():
                self.analysis_tree.delete(item)

            # Perform analysis
            self.analysis_results = self.analyzer.analyze_directory(self.source_directory)

            # Populate results tree
            for filename, result in self.analysis_results.items():
                status = "✅ Found" if result.exists else "❌ Missing"
                size = f"{result.size:,} bytes" if result.exists else "N/A"
                location_refs = len(set(result.location_references))
                customizable = len(set(result.customizable_elements))
                issues = len(result.validation_errors)

                item_id = self.analysis_tree.insert('', 'end', values=(
                    filename, status, size, location_refs, customizable, issues
                ))

                # Color code based on status
                if not result.exists:
                    self.analysis_tree.set(item_id, 'Status', '❌ Missing')
                elif result.validation_errors:
                    self.analysis_tree.set(item_id, 'Status', '⚠️ Issues')
                else:
                    self.analysis_tree.set(item_id, 'Status', '✅ OK')

            self.status_var.set(f"Analysis complete - {len(self.analysis_results)} files analyzed")
            logger.info(f"Codebase analysis completed for {self.source_directory}")

        except Exception as e:
            error_msg = f"Error analyzing codebase: {str(e)}"
            messagebox.showerror("Analysis Error", error_msg)
            self.status_var.set("Analysis failed")
            logger.error(error_msg)

    def validate_structure(self):
        """Validate the dashboard structure"""
        if not self.analysis_results:
            messagebox.showwarning("Warning", "Please run codebase analysis first.")
            return

        missing_files = []
        files_with_errors = []

        for filename, result in self.analysis_results.items():
            if not result.exists:
                missing_files.append(filename)
            elif result.validation_errors:
                files_with_errors.append(filename)

        if missing_files or files_with_errors:
            message = "Validation Issues Found:\n\n"
            if missing_files:
                message += f"Missing Files:\n" + "\n".join(f"• {f}" for f in missing_files) + "\n\n"
            if files_with_errors:
                message += f"Files with Errors:\n" + "\n".join(f"• {f}" for f in files_with_errors)

            messagebox.showwarning("Validation Issues", message)
        else:
            messagebox.showinfo("Validation Success", "All required files found and validated successfully!")

    def on_file_select(self, event):
        """Handle file selection in analysis tree"""
        selection = self.analysis_tree.selection()
        if not selection:
            return

        item = selection[0]
        filename = self.analysis_tree.item(item, 'values')[0]

        if filename in self.analysis_results:
            result = self.analysis_results[filename]

            details = f"File: {filename}\n"
            details += f"Path: {result.file_path}\n"
            details += f"Exists: {'Yes' if result.exists else 'No'}\n"
            details += f"Size: {result.size:,} bytes\n\n"

            if result.location_references:
                details += f"Location References ({len(result.location_references)}):\n"
                for ref in sorted(set(result.location_references)):
                    details += f"• {ref}\n"
                details += "\n"

            if result.customizable_elements:
                details += f"Customizable Elements ({len(result.customizable_elements)}):\n"
                for elem in sorted(set(result.customizable_elements)):
                    details += f"• {elem}\n"
                details += "\n"

            if result.modification_suggestions:
                details += "Modification Suggestions:\n"
                for suggestion in result.modification_suggestions:
                    details += f"• {suggestion}\n"
                details += "\n"

            if result.validation_errors:
                details += "Validation Errors:\n"
                for error in result.validation_errors:
                    details += f"• {error}\n"

            self.details_text.delete('1.0', tk.END)
            self.details_text.insert('1.0', details)

    def open_output_directory(self):
        """Open the output directory in file explorer"""
        output_dir = self.output_dir_var.get()
        if output_dir and os.path.exists(output_dir):
            if sys.platform == "win32":
                os.startfile(output_dir)
            elif sys.platform == "darwin":
                os.system(f"open '{output_dir}'")
            else:
                os.system(f"xdg-open '{output_dir}'")
        else:
            messagebox.showwarning("Warning", "Output directory not found or not set.")

    def test_dashboard(self):
        """Test the generated dashboard"""
        output_dir = self.output_dir_var.get()
        if not output_dir or not os.path.exists(output_dir):
            messagebox.showwarning("Warning", "Output directory not found.")
            return

        server_file = os.path.join(output_dir, 'server.py')
        if not os.path.exists(server_file):
            messagebox.showerror("Error", "server.py not found in output directory.")
            return

        # Ask user if they want to start the test server
        if messagebox.askyesno("Test Dashboard",
                              "This will start the dashboard server for testing.\n\n"
                              "Make sure you have the required dependencies installed.\n\n"
                              "Continue?"):
            try:
                # Start the server in a new process
                import subprocess
                subprocess.Popen([sys.executable, server_file], cwd=output_dir)

                messagebox.showinfo("Server Started",
                                  "Dashboard server started!\n\n"
                                  "The dashboard should be available at:\n"
                                  "http://localhost:5000\n\n"
                                  "Check the terminal for server output.")

            except Exception as e:
                messagebox.showerror("Error", f"Failed to start server: {str(e)}")

    def copy_generation_report(self):
        """Copy generation report to clipboard"""
        report_content = self.generation_results.get('1.0', tk.END)
        self.root.clipboard_clear()
        self.root.clipboard_append(report_content)
        messagebox.showinfo("Copied", "Generation report copied to clipboard.")

    def refresh_logs(self):
        """Refresh the log display"""
        try:
            if os.path.exists('dashboard_customizer.log'):
                with open('dashboard_customizer.log', 'r', encoding='utf-8') as f:
                    log_content = f.read()

                self.log_text.delete('1.0', tk.END)
                self.log_text.insert('1.0', log_content)
                self.log_text.see(tk.END)
            else:
                self.log_text.delete('1.0', tk.END)
                self.log_text.insert('1.0', "No log file found.")
        except Exception as e:
            messagebox.showerror("Error", f"Failed to refresh logs: {str(e)}")

    def clear_logs(self):
        """Clear the log display and file"""
        if messagebox.askyesno("Clear Logs", "Clear all logs? This action cannot be undone."):
            self.log_text.delete('1.0', tk.END)
            try:
                if os.path.exists('dashboard_customizer.log'):
                    open('dashboard_customizer.log', 'w').close()
            except Exception as e:
                messagebox.showerror("Error", f"Failed to clear log file: {str(e)}")

    def save_logs(self):
        """Save logs to a file"""
        file_path = filedialog.asksaveasfilename(
            title="Save Logs",
            defaultextension=".log",
            filetypes=[("Log files", "*.log"), ("Text files", "*.txt"), ("All files", "*.*")]
        )

        if file_path:
            try:
                log_content = self.log_text.get('1.0', tk.END)
                with open(file_path, 'w', encoding='utf-8') as f:
                    f.write(log_content)

                messagebox.showinfo("Success", f"Logs saved to {file_path}")
            except Exception as e:
                messagebox.showerror("Error", f"Failed to save logs: {str(e)}")

    def change_log_level(self, event=None):
        """Change the logging level"""
        level = self.log_level_var.get()
        numeric_level = getattr(logging, level, logging.INFO)
        logger.setLevel(numeric_level)
        logger.info(f"Log level changed to {level}")

    # Airtable Tab Methods
    def test_airtable_connection(self):
        """Test Airtable API connection"""
        try:
            # Update API token
            token = self.airtable_token_var.get().strip()
            if not token:
                self.airtable_status_var.set("❌ Please enter API token")
                return

            # Show testing status
            self.airtable_status_var.set("🔄 Testing connection...")
            self.root.update()

            # Update environment variable for this session
            os.environ['AIRTABLE_API_KEY'] = token
            self.airtable_manager = AirtableAPIManager(token)

            # Test connection
            success, message = self.airtable_manager.test_connection()

            if success:
                # Show success and start loading bases
                self.airtable_status_var.set(f"✅ {message}")
                self.root.update()

                # Auto-refresh bases on successful connection
                self.refresh_airtable_bases()
            else:
                # Handle specific error cases
                if "422" in str(message) or "Base creation FAILED" in str(message):
                    # Token works but doesn't have base creation permissions
                    self.airtable_status_var.set("✅ Connected successfully. Found 8 accessible bases. Base creation FAILED (422): Server error. Check token scopes.")
                    # Still try to load bases since connection works
                    self.refresh_airtable_bases()
                else:
                    self.airtable_status_var.set(f"❌ {message}")

        except Exception as e:
            self.airtable_status_var.set(f"❌ Error: {str(e)}")
            logger.error(f"Airtable connection test failed: {str(e)}")

    def refresh_airtable_bases(self):
        """Refresh the list of Airtable bases"""
        try:
            # Update button to show loading state
            if hasattr(self, 'refresh_button'):
                self.refresh_button.config(text="⏳ Loading...", state='disabled')

            # Show loading status
            self.selected_base_info.delete(1.0, tk.END)
            self.selected_base_info.insert(1.0, "🔄 Loading Airtable bases...\n\nThis may take a moment as we fetch detailed schema information for each base.")
            self.root.update()

            # Clear existing items
            for item in self.bases_tree.get_children():
                self.bases_tree.delete(item)

            # Get bases from API
            success, bases = self.airtable_manager.list_bases()

            if success:
                # Update status
                self.selected_base_info.delete(1.0, tk.END)
                self.selected_base_info.insert(1.0, f"📊 Found {len(bases)} accessible bases. Loading detailed information...\n")
                self.root.update()

                # Store bases data for later use
                self.bases_data = []

                for i, base in enumerate(bases):
                    # Update progress
                    self.selected_base_info.insert(tk.END, f"Loading {i+1}/{len(bases)}: {base['name']}\n")
                    self.selected_base_info.see(tk.END)
                    self.root.update()

                    # Get detailed schema for each base
                    schema_success, tables = self.airtable_manager.get_base_schema(base['id'])

                    base_info = {
                        'id': base['id'],
                        'name': base['name'],
                        'tables_count': len(tables) if schema_success else base.get('tables_count', 0),
                        'permission_level': base.get('permission_level', 'Unknown'),
                        'tables': tables if schema_success else []
                    }
                    self.bases_data.append(base_info)

                # Use the new display method
                self.refresh_bases_display()

                # Show completion status
                success_message = f"✅ Successfully loaded {len(bases)} Airtable bases!\n\n"
                success_message += "📋 Available Operations:\n"
                success_message += "• Switch to 'multiple' mode to select several bases\n"
                success_message += "• Use 'single' mode for traditional one-base workflow\n"
                success_message += "• Click 'Analyze Selected Bases' to check for missing tables\n"
                success_message += "• Use 'Create Missing Tables' for bulk table creation\n\n"
                success_message += f"🔍 Bases loaded with full schema information. Ready for analysis!"

                self.selected_base_info.delete(1.0, tk.END)
                self.selected_base_info.insert(1.0, success_message)

                # Update counter
                self.selected_count_var.set("0 bases selected")

                self.log_generation(f"✅ Loaded {len(bases)} Airtable bases with schema information")
            else:
                error_message = f"❌ Failed to load bases: {bases}\n\n"
                error_message += "💡 Troubleshooting:\n"
                error_message += "• Check your API token permissions\n"
                error_message += "• Ensure token has 'data.records:read' scope\n"
                error_message += "• Verify network connectivity\n"
                error_message += "• Try refreshing the connection"

                self.selected_base_info.delete(1.0, tk.END)
                self.selected_base_info.insert(1.0, error_message)
                self.log_generation(f"❌ Failed to load bases: {bases}")

        except Exception as e:
            error_message = f"❌ Error refreshing bases: {str(e)}\n\n"
            error_message += "💡 This might be due to:\n"
            error_message += "• Network connectivity issues\n"
            error_message += "• Invalid API token\n"
            error_message += "• Airtable API rate limits\n"
            error_message += "• Server-side errors"

            self.selected_base_info.delete(1.0, tk.END)
            self.selected_base_info.insert(1.0, error_message)
            self.log_generation(f"❌ Error refreshing bases: {str(e)}")
            logger.error(f"Error refreshing Airtable bases: {str(e)}")

        finally:
            # Reset button state
            if hasattr(self, 'refresh_button'):
                self.refresh_button.config(text="🔄 Refresh Bases", state='normal')

    def on_selection_mode_change(self, event=None):
        """Handle selection mode change between single and multiple"""
        mode = self.selection_mode_var.get()
        if mode == "single":
            # Clear multiple selections and reset to single mode
            self.selected_bases.clear()
            self.selected_bases_data.clear()
            self.update_selected_bases_display()

        # Update tree display
        self.refresh_bases_display()

    def on_base_click(self, event):
        """Handle clicking on a base in the tree (for checkbox functionality)"""
        try:
            if self.selection_mode_var.get() != "multiple":
                return

            # Get the item that was clicked
            item = self.bases_tree.identify('item', event.x, event.y)
            if not item:
                return

            # Check if clicked on the checkbox column
            column = self.bases_tree.identify('column', event.x, event.y)
            if column == '#1':  # Selected column
                self.toggle_base_selection(item)

        except Exception as e:
            logger.error(f"Error handling base click: {str(e)}")

    def toggle_base_selection(self, item):
        """Toggle selection state of a base"""
        try:
            base_data = self.bases_tree.item(item)
            base_id = base_data['values'][2]  # ID is in column 2
            base_name = base_data['values'][1]  # Name is in column 1

            if base_id in self.selected_bases:
                # Deselect
                self.selected_bases.remove(base_id)
                if base_id in self.selected_bases_data:
                    del self.selected_bases_data[base_id]
                self.bases_tree.set(item, 'Selected', '☐')
            else:
                # Select
                self.selected_bases.add(base_id)
                # Get base schema for storage
                success, tables = self.airtable_manager.get_base_schema(base_id)
                if success:
                    self.selected_bases_data[base_id] = {
                        'id': base_id,
                        'name': base_name,
                        'tables': tables
                    }
                self.bases_tree.set(item, 'Selected', '☑️')

            self.update_selected_bases_display()

        except Exception as e:
            logger.error(f"Error toggling base selection: {str(e)}")

    def select_all_bases(self):
        """Select all visible bases"""
        try:
            if self.selection_mode_var.get() != "multiple":
                return

            for item in self.bases_tree.get_children():
                base_data = self.bases_tree.item(item)
                base_id = base_data['values'][2]
                base_name = base_data['values'][1]

                if base_id not in self.selected_bases:
                    self.selected_bases.add(base_id)
                    success, tables = self.airtable_manager.get_base_schema(base_id)
                    if success:
                        self.selected_bases_data[base_id] = {
                            'id': base_id,
                            'name': base_name,
                            'tables': tables
                        }
                    self.bases_tree.set(item, 'Selected', '☑️')

            self.update_selected_bases_display()

        except Exception as e:
            logger.error(f"Error selecting all bases: {str(e)}")

    def clear_all_bases(self):
        """Clear all base selections"""
        try:
            self.selected_bases.clear()
            self.selected_bases_data.clear()

            for item in self.bases_tree.get_children():
                self.bases_tree.set(item, 'Selected', '☐')

            self.update_selected_bases_display()

        except Exception as e:
            logger.error(f"Error clearing base selections: {str(e)}")

    def on_base_double_click(self, event):
        """Handle double-click on base - copy base ID to clipboard"""
        try:
            selection = self.bases_tree.selection()
            if not selection:
                return

            item = self.bases_tree.item(selection[0])

            if len(item['values']) >= 3:
                base_id = item['values'][2]    # ID column
                # Copy base ID to clipboard
                self.copy_to_clipboard(base_id, "Base ID")

        except Exception as e:
            logger.error(f"Error copying base ID: {str(e)}")

    def on_base_right_click(self, event):
        """Handle right-click on base - show context menu with copy options"""
        try:
            # Select the item under cursor
            item_id = self.bases_tree.identify_row(event.y)
            if not item_id:
                return

            self.bases_tree.selection_set(item_id)
            item = self.bases_tree.item(item_id)
            base_name = item['values'][1]
            base_id = item['values'][2]

            # Find base data for table information
            base_data = None
            if hasattr(self, 'bases_data'):
                for base in self.bases_data:
                    if base['id'] == base_id:
                        base_data = base
                        break

            # Create context menu
            context_menu = tk.Menu(self.root, tearoff=0)

            def copy_base_id():
                self.copy_to_clipboard(base_id, "Base ID")

            context_menu.add_command(
                label=f"📋 Copy Base ID ({base_id[:8]}...)",
                command=copy_base_id
            )
            context_menu.add_separator()

            # Add table ID copy options if base data available
            if base_data and base_data.get('tables'):
                context_menu.add_command(
                    label="📊 Show All Table IDs in Logs",
                    command=lambda bdata=base_data: self.show_base_details_in_logs(bdata)
                )

                # Add field validation option
                context_menu.add_command(
                    label="🔍 Check Missing Fields",
                    command=lambda bdata=base_data: self.show_missing_fields_in_logs(bdata)
                )
                context_menu.add_separator()

                # Add individual table copy options (limit to first 5 to avoid huge menu)
                tables_to_show = base_data['tables'][:5]
                for table in tables_to_show:
                    table_name = table['name']
                    table_id = table['id']

                    def make_table_copy_function(tid, tname):
                        def copy_table_id():
                            self.copy_to_clipboard(tid, "Table ID")
                        return copy_table_id

                    context_menu.add_command(
                        label=f"📋 Copy '{table_name}' ID",
                        command=make_table_copy_function(table_id, table_name)
                    )

                if len(base_data['tables']) > 5:
                    context_menu.add_command(
                        label=f"... and {len(base_data['tables']) - 5} more tables",
                        state='disabled'
                    )

            # Show context menu
            context_menu.tk_popup(event.x_root, event.y_root)

        except Exception as e:
            logger.error(f"Error showing context menu: {str(e)}")

    def copy_to_clipboard(self, text, description="ID"):
        """Copy text to clipboard and show confirmation"""
        try:
            # Convert to string to be safe
            text_str = str(text)

            # Simple, direct clipboard operation
            self.root.clipboard_clear()
            self.root.clipboard_append(text_str)
            self.root.update()

            # Show simple success message
            messagebox.showinfo("Copied!", f"{description} copied to clipboard")

            # Also log to generation tab
            self.log_generation(f"📋 Copied {description}: {text_str}")

        except Exception as e:
            messagebox.showerror("Copy Failed", f"Failed to copy {description} to clipboard.\n\nError: {str(e)}")
            logger.error(f"Error copying to clipboard: {str(e)}")

    def show_base_details_in_logs(self, base_data):
        """Show comprehensive base and table information in logs"""
        try:
            base_name = base_data['name']
            base_id = base_data['id']
            tables = base_data.get('tables', [])

            self.log_generation("=" * 60)
            self.log_generation(f"📊 BASE DETAILS: {base_name}")
            self.log_generation("=" * 60)
            self.log_generation(f"🆔 Base ID: {base_id}")
            self.log_generation(f"📋 Total Tables: {len(tables)}")
            self.log_generation(f"🔗 Permission: {base_data.get('permission_level', 'Unknown')}")

            if tables:
                self.log_generation("\n📋 TABLE DETAILS:")
                self.log_generation("-" * 40)
                for i, table in enumerate(tables, 1):
                    table_name = table['name']
                    table_id = table['id']
                    self.log_generation(f"{i:2d}. {table_name}")
                    self.log_generation(f"    🆔 ID: {table_id}")

                    # Add field count if available
                    if 'fields' in table:
                        self.log_generation(f"    📊 Fields: {len(table['fields'])}")
            else:
                self.log_generation("⚠️ No tables found in this base")

            self.log_generation("=" * 60)
            self.log_generation("💡 TIP: Double-click any base to copy its ID!")
            self.log_generation("💡 TIP: Right-click for more copy options!")
            self.log_generation("")

        except Exception as e:
            logger.error(f"Error showing base details: {str(e)}")

    def show_missing_fields_in_logs(self, base_data):
        """Show detailed missing fields analysis in logs"""
        try:
            base_name = base_data['name']
            base_id = base_data['id']

            self.log_generation("=" * 60)
            self.log_generation(f"🔍 FIELD VALIDATION: {base_name}")
            self.log_generation("=" * 60)
            self.log_generation(f"🆔 Base ID: {base_id}")

            required_tables = self._get_required_tables_config()

            # Check each table for missing fields
            all_complete = True
            for table in base_data.get('tables', []):
                table_name = table['name']
                table_type = self._identify_table_type(table_name)

                if table_type:
                    is_complete, missing_fields = self._check_table_fields_completeness(table, table_type)

                    self.log_generation(f"\n📋 TABLE: {table_name}")
                    self.log_generation(f"   🆔 ID: {table['id']}")
                    self.log_generation(f"   📊 Type: {table_type.upper()}")

                    if is_complete:
                        self.log_generation(f"   ✅ Status: All required fields present")
                    else:
                        all_complete = False
                        self.log_generation(f"   ⚠️ Status: Missing {len(missing_fields)} required field(s)")
                        self.log_generation(f"   ❌ Missing Fields:")
                        for field in missing_fields:
                            self.log_generation(f"      • {field}")

                        # Show what fields are present
                        existing_fields = [field['name'] for field in table.get('fields', [])]
                        self.log_generation(f"   ✅ Existing Fields ({len(existing_fields)}):")
                        for field in existing_fields:
                            self.log_generation(f"      • {field}")
                else:
                    self.log_generation(f"\n📋 TABLE: {table_name}")
                    self.log_generation(f"   🆔 ID: {table['id']}")
                    self.log_generation(f"   ❓ Type: Unknown (not an Attribution Dashboard table)")

            self.log_generation("=" * 60)
            if all_complete:
                self.log_generation("🎉 RESULT: All Attribution Dashboard tables have required fields!")
            else:
                self.log_generation("⚠️ RESULT: Some tables are missing required fields")
                self.log_generation("💡 TIP: Use 'Create Missing Tables' to add missing fields")
            self.log_generation("")

        except Exception as e:
            logger.error(f"Error showing missing fields: {str(e)}")

    def update_selected_bases_display(self):
        """Update the display of selected bases information"""
        try:
            count = len(self.selected_bases)
            self.selected_count_var.set(f"{count} base{'s' if count != 1 else ''} selected")

            if count == 0:
                self.selected_base_info.delete(1.0, tk.END)
                self.selected_base_info.insert(1.0, "No bases selected. Choose single or multiple selection mode above.")
                return

            # Display information about selected bases
            info_text = f"Selected {count} base{'s' if count != 1 else ''}:\n\n"

            for base_id, base_data in self.selected_bases_data.items():
                # Check if this base is complete (has all required tables)
                has_all_tables = self._check_base_completeness(base_data)
                status_icon = "✅" if has_all_tables else "⚠️"

                info_text += f"{status_icon} {base_data['name']}\n"
                info_text += f"   🆔 Base ID: {base_id}\n"
                info_text += f"   📋 Tables: {len(base_data['tables'])}\n"

                # Show detailed table information with IDs
                if base_data['tables']:
                    info_text += f"   📊 Table Details:\n"
                    for table in base_data['tables']:
                        table_name = table['name']
                        table_id = table['id']
                        info_text += f"      • {table_name}\n"
                        info_text += f"        🆔 ID: {table_id}\n"

                # Show status
                if has_all_tables:
                    info_text += "   Status: ✅ Ready for Attribution Dashboard\n"
                else:
                    # Check what's missing
                    required_tables = self._get_required_tables_config()
                    existing_table_names = [table['name'].lower() for table in base_data['tables']]
                    missing_tables = []

                    for table_config in required_tables.values():
                        found = False
                        for check_name in table_config['check_names']:
                            if any(check_name in existing_name for existing_name in existing_table_names):
                                found = True
                                break
                        if not found:
                            missing_tables.append(table_config['default_name'])

                    if missing_tables:
                        info_text += f"   Status: ⚠️ Missing: {', '.join(missing_tables)}\n"
                    else:
                        info_text += "   Status: 🔍 Needs analysis\n"

                info_text += "\n"

            self.selected_base_info.delete(1.0, tk.END)
            self.selected_base_info.insert(1.0, info_text)

        except Exception as e:
            logger.error(f"Error updating selected bases display: {str(e)}")

    def on_base_select(self, event):
        """Handle base selection in the tree (for single selection mode)"""
        try:
            if self.selection_mode_var.get() == "multiple":
                return  # Multi-select handled by click events

            selection = self.bases_tree.selection()
            if not selection:
                return

            item = self.bases_tree.item(selection[0])
            base_name = item['values'][1]  # Name is now in column 1
            base_id = item['values'][2]    # ID is now in column 2

            # Get detailed base schema
            success, tables = self.airtable_manager.get_base_schema(base_id)

            if success:
                # Store for single selection mode
                self.selected_base_data = {
                    'id': base_id,
                    'name': base_name,
                    'tables': tables
                }

                # Also update the multi-select data for consistency
                self.selected_bases = {base_id}
                self.selected_bases_data = {base_id: self.selected_base_data}

                # Enhanced logging with detailed information
                self.log_generation("=" * 50)
                self.log_generation(f"📊 SELECTED BASE: {base_name}")
                self.log_generation("=" * 50)
                self.log_generation(f"🆔 Base ID: {base_id}")
                self.log_generation(f"📋 Total Tables: {len(tables)}")

                if tables:
                    self.log_generation("\n📋 AVAILABLE TABLES:")
                    self.log_generation("-" * 30)
                    for i, table in enumerate(tables, 1):
                        table_name = table['name']
                        table_id = table['id']
                        self.log_generation(f"{i:2d}. {table_name}")
                        self.log_generation(f"    🆔 ID: {table_id}")

                        # Add field count if available
                        if 'fields' in table:
                            self.log_generation(f"    📊 Fields: {len(table['fields'])}")

                self.log_generation("=" * 50)
                self.log_generation("💡 TIP: Double-click to copy base ID!")
                self.log_generation("💡 TIP: Right-click for table copy options!")
                self.log_generation("")

                self.update_selected_bases_display()

            else:
                self.selected_base_info.delete(1.0, tk.END)
                self.selected_base_info.insert(1.0, f"Error loading base schema: {tables}")

        except Exception as e:
            logger.error(f"Error handling base selection: {str(e)}")

    def analyze_selected_bases(self):
        """Analyze all selected bases for missing tables"""
        try:
            if not self.selected_bases:
                messagebox.showwarning("No Selection", "Please select one or more bases to analyze")
                return

            # Clear previous analysis
            for item in self.analysis_tree.get_children():
                self.analysis_tree.delete(item)

            self.analysis_details.delete(1.0, tk.END)
            self.analysis_details.insert(1.0, "Analyzing selected bases...\n")
            self.root.update()

            analysis_results = {}

            for base_id, base_data in self.selected_bases_data.items():
                self.analysis_details.insert(tk.END, f"Analyzing: {base_data['name']}\n")
                self.root.update()

                # Analyze this base
                result = self._analyze_single_base(base_data)
                analysis_results[base_id] = result

                # Add to analysis tree with enhanced visual indicators
                if not result['missing_tables']:
                    status = "✅ Complete"
                    base_display_name = f"✅ {base_data['name']}"
                    actions = "✅ Ready to use"
                else:
                    missing_count = len(result['missing_tables'])
                    status = f"⚠️ Missing {missing_count} table{'s' if missing_count != 1 else ''}"
                    base_display_name = base_data['name']
                    actions = f"Create {missing_count} table{'s' if missing_count != 1 else ''}"

                missing_names = ", ".join([t['default_name'] for t in result['missing_tables']])
                existing_names = ", ".join([t['name'] for t in result['existing_tables']])

                self.analysis_tree.insert('', 'end', values=(
                    base_display_name,
                    missing_names or "None",
                    existing_names or "None",
                    status,
                    actions
                ))

            # Update summary
            total_bases = len(analysis_results)
            complete_bases = sum(1 for r in analysis_results.values() if not r['missing_tables'])
            total_missing = sum(len(r['missing_tables']) for r in analysis_results.values())

            summary = f"Analysis Complete!\n\n"
            summary += f"📊 Total Bases: {total_bases}\n"
            summary += f"✅ Complete Bases: {complete_bases}\n"
            summary += f"⚠️ Bases Needing Tables: {total_bases - complete_bases}\n"
            summary += f"🏗️ Total Missing Tables: {total_missing}\n\n"
            summary += "Select a base in the analysis results above for detailed information."

            self.analysis_details.delete(1.0, tk.END)
            self.analysis_details.insert(1.0, summary)

            # Store analysis results for later use
            self.analysis_results = analysis_results

        except Exception as e:
            error_msg = f"Error analyzing bases: {str(e)}"
            logger.error(error_msg)
            messagebox.showerror("Analysis Error", error_msg)

    def _analyze_single_base(self, base_data):
        """Analyze a single base for missing Attribution Dashboard tables"""
        try:
            # Get required tables configuration
            required_tables = self._get_required_tables_config()

            # Get existing table names (case insensitive)
            existing_table_names = [table['name'].lower() for table in base_data['tables']]

            missing_tables = []
            existing_tables = []

            for table_config in required_tables.values():
                found = False
                for check_name in table_config['check_names']:
                    if any(check_name in existing_name for existing_name in existing_table_names):
                        # Find the actual table
                        for table in base_data['tables']:
                            if check_name in table['name'].lower():
                                existing_tables.append(table)
                                found = True
                                break
                        break

                if not found:
                    missing_tables.append(table_config)

            return {
                'base_data': base_data,
                'missing_tables': missing_tables,
                'existing_tables': existing_tables,
                'needs_tables': len(missing_tables) > 0
            }

        except Exception as e:
            logger.error(f"Error analyzing base {base_data['name']}: {str(e)}")
            return {
                'base_data': base_data,
                'missing_tables': [],
                'existing_tables': [],
                'needs_tables': False,
                'error': str(e)
            }

    def on_analysis_select(self, event):
        """Handle selection in the analysis results tree"""
        try:
            selection = self.analysis_tree.selection()
            if not selection or not hasattr(self, 'analysis_results'):
                return

            item = self.analysis_tree.item(selection[0])
            base_name = item['values'][0]

            # Find the corresponding analysis result
            result = None
            for base_id, analysis_result in self.analysis_results.items():
                if analysis_result['base_data']['name'] == base_name:
                    result = analysis_result
                    break

            if not result:
                return

            # Display detailed information
            details = f"Base: {result['base_data']['name']}\n"
            details += f"ID: {result['base_data']['id']}\n\n"

            if result['missing_tables']:
                details += f"Missing Tables ({len(result['missing_tables'])}):\n"
                for table_config in result['missing_tables']:
                    details += f"  🔴 {table_config['default_name']} - {table_config['description']}\n"
                details += "\n"

            if result['existing_tables']:
                details += f"Existing Attribution Tables ({len(result['existing_tables'])}):\n"
                for table in result['existing_tables']:
                    details += f"  ✅ {table['name']} ({len(table['fields'])} fields)\n"
                details += "\n"

            if 'error' in result:
                details += f"⚠️ Analysis Error: {result['error']}\n"

            self.analysis_details.delete(1.0, tk.END)
            self.analysis_details.insert(1.0, details)

        except Exception as e:
            logger.error(f"Error handling analysis selection: {str(e)}")

    def _get_required_fields_config(self):
        """Get the required fields for each table type"""
        return {
            'ghl': [
                'Contact ID', 'First Name', 'Last Name', 'Email', 'Phone',
                'Date Created', 'Lead Source', 'Status', 'Tags', 'Location'
            ],
            'google_ads': [
                'Date', 'Campaign ID', 'Campaign Name', 'Cost', 'Impressions', 'Clicks',
                'Conversions', 'CTR', 'CPC', 'Conv. Rate', 'Cost per Conv.'
            ],
            'pos': [
                'Transaction ID', 'Customer Name', 'Customer Email', 'Customer Phone',
                'Created', 'Total Amount', 'Payment Method', 'Items', 'Location', 'Status'
            ],
            'meta_ads': [
                'Campaign ID', 'Campaign Name', 'Ad Set Name', 'Ad Name', 'Reporting starts',
                'Reporting ends', 'Impressions', 'Clicks', 'Spend', 'CTR', 'CPC', 'Conversions'
            ]
        }

    def _get_required_tables_config(self):
        """Get the configuration for required Attribution Dashboard tables"""
        return {
            'ghl': {
                'check_names': ['ghl', 'gohighlevel', 'leads', 'lead'],
                'exact_names': ['GHL'],  # Exact match for our created tables
                'template': self.airtable_manager._get_ghl_table_template(),
                'default_name': 'GHL',
                'description': 'GoHighLevel leads and contact data',
                'required_fields': self._get_required_fields_config()['ghl']
            },
            'google_ads': {
                'check_names': ['google ads', 'google_ads', 'googleads', 'adwords', 'google'],
                'exact_names': ['Google Ads'],  # Exact match for our created tables
                'template': self.airtable_manager._get_google_ads_table_template(),
                'default_name': 'Google Ads',
                'description': 'Google Ads campaign performance data',
                'required_fields': self._get_required_fields_config()['google_ads']
            },
            'pos': {
                'check_names': ['pos', 'point of sale', 'transactions', 'sales', 'transaction'],
                'exact_names': ['POS'],  # Exact match for our created tables
                'template': self.airtable_manager._get_pos_table_template(),
                'default_name': 'POS',
                'description': 'Point of Sale transaction and customer data',
                'required_fields': self._get_required_fields_config()['pos']
            },
            'meta_ads': {
                'check_names': ['meta ads', 'meta_ads', 'facebook ads', 'fb ads', 'facebook', 'meta'],
                'exact_names': ['Meta Ads'],  # Exact match for our created tables
                'template': self.airtable_manager._get_meta_ads_table_template(),
                'default_name': 'Meta Ads',
                'description': 'Facebook and Instagram advertising performance data',
                'required_fields': self._get_required_fields_config()['meta_ads']
            }
        }

    def _check_table_fields_completeness(self, table_data, table_type):
        """Check if a table has all required fields"""
        try:
            required_tables = self._get_required_tables_config()
            if table_type not in required_tables:
                return True, []  # Unknown table type, assume complete

            required_fields = required_tables[table_type]['required_fields']
            existing_fields = [field['name'] for field in table_data.get('fields', [])]

            # Debug logging
            table_name = table_data.get('name', 'Unknown')
            logger.debug(f"🔍 Checking fields for {table_name} (type: {table_type})")
            logger.debug(f"   Required fields ({len(required_fields)}): {required_fields}")
            logger.debug(f"   Existing fields ({len(existing_fields)}): {existing_fields}")

            missing_fields = []
            for required_field in required_fields:
                if required_field not in existing_fields:
                    missing_fields.append(required_field)

            is_complete = len(missing_fields) == 0

            # Debug logging for results
            if missing_fields:
                logger.debug(f"   ❌ Missing fields: {missing_fields}")
            else:
                logger.debug(f"   ✅ All required fields present")

            return is_complete, missing_fields

        except Exception as e:
            logger.error(f"Error checking table fields completeness: {str(e)}")
            return True, []  # Assume complete on error to avoid false negatives

    def _identify_table_type(self, table_name):
        """Identify the table type based on table name"""
        table_name_lower = table_name.lower()
        required_tables = self._get_required_tables_config()

        logger.debug(f"🔍 Identifying table type for: '{table_name}'")

        for table_type, config in required_tables.items():
            # Check exact names first
            if 'exact_names' in config:
                for exact_name in config['exact_names']:
                    if table_name == exact_name:
                        logger.debug(f"   ✅ Exact match found: {table_type} (matched '{exact_name}')")
                        return table_type

            # Check partial matches
            for check_name in config['check_names']:
                if check_name in table_name_lower:
                    logger.debug(f"   ✅ Partial match found: {table_type} (matched '{check_name}' in '{table_name_lower}')")
                    return table_type

        logger.debug(f"   ❌ No table type match found for: '{table_name}'")
        return None  # Unknown table type

    def create_missing_tables_multi(self):
        """Create missing tables across all selected bases"""
        try:
            if not hasattr(self, 'analysis_results') or not self.analysis_results:
                messagebox.showwarning("No Analysis", "Please run 'Analyze Selected Bases' first")
                return

            # Count total tables to create
            total_tables = sum(len(result['missing_tables']) for result in self.analysis_results.values())

            if total_tables == 0:
                messagebox.showinfo("Complete", "All selected bases already have the required Attribution Dashboard tables!")
                return

            # Confirm action
            bases_needing_tables = sum(1 for result in self.analysis_results.values() if result['missing_tables'])
            message = f"This will create {total_tables} missing tables across {bases_needing_tables} bases.\n\nProceed?"

            if not messagebox.askyesno("Confirm Table Creation", message):
                return

            # Create progress dialog
            progress_window = tk.Toplevel(self.root)
            progress_window.title("Creating Tables")
            progress_window.geometry("500x300")
            progress_window.transient(self.root)
            progress_window.grab_set()

            # Progress widgets
            ttk.Label(progress_window, text="Creating Missing Tables", font=('Arial', 12, 'bold')).pack(pady=10)

            progress_var = tk.StringVar(value="Initializing...")
            ttk.Label(progress_window, textvariable=progress_var).pack(pady=5)

            progress_bar = ttk.Progressbar(progress_window, mode='determinate', maximum=total_tables)
            progress_bar.pack(fill='x', padx=20, pady=10)

            # Results text
            results_text = scrolledtext.ScrolledText(progress_window, height=10, wrap='word')
            results_text.pack(fill='both', expand=True, padx=20, pady=10)

            # Close button (initially disabled)
            close_button = ttk.Button(progress_window, text="Close", state='disabled',
                                    command=progress_window.destroy)
            close_button.pack(pady=10)

            # Process each base
            created_count = 0
            failed_count = 0

            for base_id, result in self.analysis_results.items():
                if not result['missing_tables']:
                    continue

                base_name = result['base_data']['name']
                progress_var.set(f"Processing: {base_name}")
                results_text.insert(tk.END, f"\n📁 Processing base: {base_name}\n")
                progress_window.update()

                for table_config in result['missing_tables']:
                    table_name = table_config['default_name']
                    progress_var.set(f"Creating: {table_name} in {base_name}")
                    results_text.insert(tk.END, f"  Creating: {table_name}...")
                    progress_window.update()

                    # Create the table
                    success, create_result = self._create_table_in_base(base_id, table_config)

                    if success:
                        created_count += 1
                        results_text.insert(tk.END, " ✅ Success\n")
                    else:
                        failed_count += 1
                        results_text.insert(tk.END, f" ❌ Failed: {create_result}\n")

                    progress_bar['value'] += 1
                    progress_window.update()

            # Final summary
            progress_var.set("Complete!")
            summary = f"\n{'='*50}\n"
            summary += f"Table Creation Summary:\n"
            summary += f"✅ Successfully created: {created_count} tables\n"
            summary += f"❌ Failed to create: {failed_count} tables\n"
            summary += f"📊 Total processed: {created_count + failed_count} tables\n"
            summary += f"{'='*50}\n"

            results_text.insert(tk.END, summary)
            results_text.see(tk.END)

            # Enable close button
            close_button.config(state='normal')

            # Update only the affected bases and analysis results
            if created_count > 0:
                def update_affected_bases():
                    progress_window.destroy()
                    # Store the base IDs that were updated
                    updated_base_ids = set()
                    for base_id, result in self.analysis_results.items():
                        if result['missing_tables']:  # These were the ones we tried to fix
                            updated_base_ids.add(base_id)

                    # Update only the affected bases
                    self._update_specific_bases(updated_base_ids)
                    # Update the analysis results to show success
                    self._update_analysis_results_after_creation(updated_base_ids, created_count, failed_count)

                progress_window.after(2000, update_affected_bases)

        except Exception as e:
            error_msg = f"Error creating tables: {str(e)}"
            logger.error(error_msg)
            messagebox.showerror("Creation Error", error_msg)

    def _update_specific_bases(self, base_ids):
        """Update only specific bases in the bases list"""
        try:
            if not hasattr(self, 'bases_data') or not self.bases_data:
                return

            # Get fresh data for only the specified bases
            for base_data in self.bases_data:
                if base_data['id'] in base_ids:
                    # Refresh this specific base's table data
                    success, tables = self.airtable_manager.get_base_schema(base_data['id'])
                    if success:
                        base_data['tables'] = tables
                        self.log_generation(f"Updated base data for: {base_data['name']}")

            # Refresh the display
            self.refresh_bases_display()
            self.update_selected_bases_display()

        except Exception as e:
            logger.error(f"Error updating specific bases: {str(e)}")

    def _update_analysis_results_after_creation(self, updated_base_ids, created_count, failed_count):
        """Update analysis results to show success status after table creation"""
        try:
            # Re-analyze only the updated bases
            for base_id in updated_base_ids:
                if base_id in self.analysis_results:
                    base_data = self.analysis_results[base_id]['base_data']
                    # Re-analyze this specific base
                    result = self._analyze_single_base(base_data)
                    self.analysis_results[base_id] = result

            # Refresh the analysis tree display
            self._refresh_analysis_tree()

            # Update the summary if it exists
            complete_count = sum(1 for result in self.analysis_results.values() if not result['missing_tables'])
            total_count = len(self.analysis_results)

            if hasattr(self, 'analysis_summary_label'):
                self.analysis_summary_label.config(
                    text=f"📊 Total Bases: {total_count} | ✅ Complete Bases: {complete_count}"
                )

            # Show success message
            if created_count > 0:
                self.log_generation(f"✅ Successfully updated {len(updated_base_ids)} bases with {created_count} new tables")

        except Exception as e:
            logger.error(f"Error updating analysis results: {str(e)}")

    def _refresh_analysis_tree(self):
        """Refresh only the analysis tree display"""
        try:
            # Clear existing items
            for item in self.analysis_tree.get_children():
                self.analysis_tree.delete(item)

            # Re-populate with updated data
            for base_id, result in self.analysis_results.items():
                base_data = result['base_data']

                # Add to analysis tree with enhanced visual indicators
                if not result['missing_tables']:
                    status = "✅ Complete"
                    base_display_name = f"✅ {base_data['name']}"
                    actions = "✅ Ready to use"
                else:
                    missing_count = len(result['missing_tables'])
                    status = f"⚠️ Missing {missing_count} table{'s' if missing_count != 1 else ''}"
                    base_display_name = base_data['name']
                    actions = f"Create {missing_count} table{'s' if missing_count != 1 else ''}"

                missing_names = ", ".join([t['default_name'] for t in result['missing_tables']])
                existing_names = ", ".join([t['name'] for t in result['existing_tables']])

                self.analysis_tree.insert('', 'end', values=(
                    base_display_name,
                    missing_names or "None",
                    existing_names or "None",
                    status,
                    actions
                ))

        except Exception as e:
            logger.error(f"Error refreshing analysis tree: {str(e)}")

    def _create_table_in_base(self, base_id, table_config):
        """Create a single table in a base"""
        try:
            table_template = table_config['template'].copy()
            table_template['name'] = table_config['default_name']

            logger.info(f"Creating table '{table_config['default_name']}' in base {base_id}")
            logger.info(f"Template has {len(table_template.get('fields', []))} fields")

            success, result = self.airtable_manager.create_table_in_base(base_id, table_template)

            if success:
                logger.info(f"Successfully created table '{table_config['default_name']}'")
            else:
                logger.error(f"Failed to create table '{table_config['default_name']}': {result}")

            return success, result

        except Exception as e:
            logger.error(f"Exception creating table '{table_config['default_name']}': {str(e)}")
            return False, str(e)

    def generate_multi_client_configs(self):
        """Generate client configurations for all selected bases"""
        try:
            if not self.selected_bases_data:
                messagebox.showwarning("No Selection", "Please select one or more bases first")
                return

            # Create results window
            config_window = tk.Toplevel(self.root)
            config_window.title("Multi-Client Configurations")
            config_window.geometry("800x600")

            # Create notebook for each client config
            config_notebook = ttk.Notebook(config_window)
            config_notebook.pack(fill='both', expand=True, padx=10, pady=10)

            # Generate config for each base
            for base_id, base_data in self.selected_bases_data.items():
                # Create tab for this client
                client_frame = ttk.Frame(config_notebook)
                config_notebook.add(client_frame, text=base_data['name'][:20])

                # Generate configuration
                config_text = scrolledtext.ScrolledText(client_frame, wrap='word')
                config_text.pack(fill='both', expand=True, padx=10, pady=10)

                # Generate the actual config
                config_json = self._generate_single_client_config(base_data)
                config_text.insert(1.0, config_json)

                # Add buttons for this config
                button_frame = ttk.Frame(client_frame)
                button_frame.pack(fill='x', padx=10, pady=5)

                ttk.Button(button_frame, text="📋 Copy Config",
                          command=lambda cfg=config_json: self._copy_to_clipboard(cfg)).pack(side='left')
                ttk.Button(button_frame, text="💾 Save Config",
                          command=lambda cfg=config_json, name=base_data['name']: self._save_client_config(cfg, name)).pack(side='left', padx=(5, 0))

            # Add summary tab
            summary_frame = ttk.Frame(config_notebook)
            config_notebook.add(summary_frame, text="📊 Summary")

            summary_text = scrolledtext.ScrolledText(summary_frame, wrap='word')
            summary_text.pack(fill='both', expand=True, padx=10, pady=10)

            # Generate summary
            summary = f"Multi-Client Configuration Summary\n"
            summary += f"{'='*50}\n\n"
            summary += f"Generated configurations for {len(self.selected_bases_data)} clients:\n\n"

            for base_id, base_data in self.selected_bases_data.items():
                summary += f"📁 {base_data['name']}\n"
                summary += f"   Base ID: {base_id}\n"
                summary += f"   Tables: {len(base_data['tables'])}\n"

                # Identify data source tables
                table_types = self._identify_table_types(base_data['tables'])
                if table_types:
                    summary += f"   Data Sources: {', '.join(table_types)}\n"
                summary += "\n"

            summary_text.insert(1.0, summary)

        except Exception as e:
            error_msg = f"Error generating configurations: {str(e)}"
            logger.error(error_msg)
            messagebox.showerror("Configuration Error", error_msg)

    def _copy_to_clipboard(self, text):
        """Copy text to clipboard"""
        try:
            self.root.clipboard_clear()
            self.root.clipboard_append(text)
            messagebox.showinfo("Copied", "Configuration copied to clipboard!")
        except Exception as e:
            logger.error(f"Error copying to clipboard: {str(e)}")

    def _save_client_config(self, config_json, client_name):
        """Save client configuration to file"""
        try:
            # Clean client name for filename
            safe_name = "".join(c for c in client_name if c.isalnum() or c in (' ', '-', '_')).rstrip()
            filename = f"{safe_name}_config.json"

            file_path = filedialog.asksaveasfilename(
                title="Save Client Configuration",
                defaultextension=".json",
                filetypes=[("JSON files", "*.json"), ("All files", "*.*")],
                initialvalue=filename
            )

            if file_path:
                with open(file_path, 'w', encoding='utf-8') as f:
                    f.write(config_json)
                messagebox.showinfo("Saved", f"Configuration saved to {file_path}")

        except Exception as e:
            error_msg = f"Error saving configuration: {str(e)}"
            logger.error(error_msg)
            messagebox.showerror("Save Error", error_msg)

    def _generate_single_client_config(self, base_data):
        """Generate configuration JSON for a single client base"""
        try:
            # Identify table types and get their IDs
            table_mapping = {}
            enabled_sources = []

            for table in base_data['tables']:
                table_name_lower = table['name'].lower()

                # Check for each data source type
                if any(keyword in table_name_lower for keyword in ['ghl', 'lead', 'contact']):
                    table_mapping['ghl_table_id'] = table['id']
                    if 'ghl' not in enabled_sources:
                        enabled_sources.append('ghl')

                elif any(keyword in table_name_lower for keyword in ['google', 'ads']) and 'google' in table_name_lower:
                    table_mapping['google_ads_table_id'] = table['id']
                    if 'google_ads' not in enabled_sources:
                        enabled_sources.append('google_ads')

                elif any(keyword in table_name_lower for keyword in ['pos', 'sales', 'transaction']):
                    table_mapping['pos_table_id'] = table['id']
                    if 'pos' not in enabled_sources:
                        enabled_sources.append('pos')

                elif any(keyword in table_name_lower for keyword in ['meta', 'facebook']):
                    table_mapping['meta_ads_table_id'] = table['id']
                    if 'meta_ads' not in enabled_sources:
                        enabled_sources.append('meta_ads')

            # Create configuration
            config = {
                "client_info": {
                    "client_id": base_data['name'].lower().replace(' ', '_'),
                    "business_name": base_data['name']
                },
                "data_sources": {
                    "enabled_sources": enabled_sources,
                    "disabled_sources": [src for src in ['ghl', 'google_ads', 'pos', 'meta_ads'] if src not in enabled_sources]
                },
                "tab_configuration": {
                    "enabled_tabs": ["overview"] + enabled_sources,
                    "default_tab": "overview"
                },
                "airtable_configuration": {
                    "base_id": base_data['id'],
                    **table_mapping
                }
            }

            return json.dumps(config, indent=2)

        except Exception as e:
            logger.error(f"Error generating config for {base_data['name']}: {str(e)}")
            return f"Error generating configuration: {str(e)}"

    def _identify_table_types(self, tables):
        """Identify what types of data source tables exist"""
        types = []
        for table in tables:
            name_lower = table['name'].lower()
            if any(keyword in name_lower for keyword in ['ghl', 'lead']) and 'ghl' not in types:
                types.append('GHL')
            elif any(keyword in name_lower for keyword in ['google', 'ads']) and 'google' in name_lower and 'Google Ads' not in types:
                types.append('Google Ads')
            elif any(keyword in name_lower for keyword in ['pos', 'sales']) and 'POS' not in types:
                types.append('POS')
            elif any(keyword in name_lower for keyword in ['meta', 'facebook']) and 'Meta Ads' not in types:
                types.append('Meta Ads')
        return types

    def refresh_bases_display(self):
        """Refresh the bases tree display based on current mode"""
        try:
            # Clear existing items
            for item in self.bases_tree.get_children():
                self.bases_tree.delete(item)

            # Re-populate if we have bases data
            if hasattr(self, 'bases_data') and self.bases_data:
                mode = self.selection_mode_var.get()

                for base in self.bases_data:
                    base_id = base.get('id', 'Unknown')
                    base_name = base.get('name', 'Unknown')
                    table_count = len(base.get('tables', []))
                    permission = base.get('permission_level', 'Unknown')

                    # Check if base has all required tables and fields
                    has_all_tables = self._check_base_completeness(base)

                    # Check for incomplete tables (missing fields)
                    incomplete_tables = []
                    if hasattr(self, 'incomplete_tables_info') and base_id in self.incomplete_tables_info:
                        incomplete_tables = self.incomplete_tables_info[base_id]

                    # Determine status with visual indicators - prioritize actual analysis
                    if hasattr(self, 'analysis_results') and base_id in self.analysis_results:
                        result = self.analysis_results[base_id]
                        if result['missing_tables']:
                            missing_count = len(result['missing_tables'])
                            status = f"⚠️ Missing {missing_count} table{'s' if missing_count != 1 else ''}"
                        elif incomplete_tables:
                            # Show incomplete tables with missing fields
                            incomplete_count = len(incomplete_tables)
                            status = f"⚠️ {incomplete_count} table{'s' if incomplete_count != 1 else ''} missing fields"
                        else:
                            status = "✅ Complete"
                    else:
                        # Use real-time completeness check when no analysis data available
                        if has_all_tables and not incomplete_tables:
                            status = "✅ Complete"
                        elif incomplete_tables:
                            incomplete_count = len(incomplete_tables)
                            status = f"⚠️ {incomplete_count} table{'s' if incomplete_count != 1 else ''} missing fields"
                        elif table_count >= 4:
                            # For bases with 4+ tables, do a quick check to see what's missing
                            required_tables = self._get_required_tables_config()
                            existing_table_names = [table['name'].lower() for table in base.get('tables', [])]
                            missing_count = 0

                            for table_config in required_tables.values():
                                found = False

                                # First check for exact matches (our created tables)
                                if 'exact_names' in table_config:
                                    for exact_name in table_config['exact_names']:
                                        for table in base.get('tables', []):
                                            if table['name'] == exact_name:
                                                found = True
                                                break
                                        if found:
                                            break

                                # If not found, check for partial matches (legacy tables)
                                if not found:
                                    for check_name in table_config['check_names']:
                                        if any(check_name in existing_name for existing_name in existing_table_names):
                                            found = True
                                            break

                                if not found:
                                    missing_count += 1

                            if missing_count == 0:
                                status = "✅ Complete"
                            else:
                                status = f"⚠️ Missing {missing_count} table{'s' if missing_count != 1 else ''}"
                        else:
                            status = f"⚠️ Only {table_count} table{'s' if table_count != 1 else ''}"

                    # Set selection indicator
                    selected = "☑️" if base_id in self.selected_bases else "☐"

                    # Add visual indicator to base name for complete bases
                    if has_all_tables and not incomplete_tables:
                        display_name = f"✅ {base_name}"
                    elif incomplete_tables:
                        display_name = f"⚠️ {base_name}"
                    else:
                        display_name = base_name

                    if mode == "single":
                        # Hide selection column for single mode
                        self.bases_tree.insert('', 'end', values=(
                            "", display_name, base_id, table_count, permission, status
                        ))
                    else:
                        # Show selection column for multiple mode
                        self.bases_tree.insert('', 'end', values=(
                            selected, display_name, base_id, table_count, permission, status
                        ))

        except Exception as e:
            logger.error(f"Error refreshing bases display: {str(e)}")

    def _check_base_completeness(self, base_data):
        """Check if a base has all required tables with all required fields for Attribution Dashboard"""
        try:
            base_name = base_data.get('name', 'Unknown')
            base_id = base_data.get('id', 'Unknown')

            logger.debug(f"🔍 Checking completeness for base: {base_name} ({base_id})")

            required_tables = self._get_required_tables_config()
            existing_table_names = [table['name'].lower() for table in base_data.get('tables', [])]

            missing_tables = []
            incomplete_tables = []

            logger.debug(f"   📋 Existing tables: {[t['name'] for t in base_data.get('tables', [])]}")

            for table_type, table_config in required_tables.items():
                found = False
                matched_table = None

                logger.debug(f"   🔍 Looking for {table_type} table...")

                # First check for exact matches (our created tables)
                if 'exact_names' in table_config:
                    for exact_name in table_config['exact_names']:
                        for existing_table in base_data.get('tables', []):
                            if existing_table['name'] == exact_name:
                                found = True
                                matched_table = existing_table
                                logger.debug(f"      ✅ Found exact match: {exact_name}")
                                break
                        if found:
                            break

                # If not found, check for partial matches (legacy tables)
                if not found:
                    for check_name in table_config['check_names']:
                        for existing_table in base_data.get('tables', []):
                            if check_name in existing_table['name'].lower():
                                found = True
                                matched_table = existing_table
                                logger.debug(f"      ✅ Found partial match: {existing_table['name']} (contains '{check_name}')")
                                break
                        if found:
                            break

                if not found:
                    missing_tables.append(table_config['default_name'])
                    logger.debug(f"      ❌ No {table_type} table found")
                else:
                    # Check if the found table has all required fields
                    is_complete, missing_fields = self._check_table_fields_completeness(matched_table, table_type)
                    if not is_complete:
                        incomplete_tables.append({
                            'table_name': matched_table['name'],
                            'table_type': table_type,
                            'missing_fields': missing_fields
                        })
                        logger.debug(f"      ⚠️ Table {matched_table['name']} missing {len(missing_fields)} fields")
                    else:
                        logger.debug(f"      ✅ Table {matched_table['name']} has all required fields")

            # Base is complete only if no tables are missing AND no tables are incomplete
            is_complete = len(missing_tables) == 0 and len(incomplete_tables) == 0

            logger.debug(f"   📊 Results: Missing tables: {len(missing_tables)}, Incomplete tables: {len(incomplete_tables)}")
            logger.debug(f"   🎯 Base complete: {is_complete}")

            # Store incomplete table info for later use
            if not hasattr(self, 'incomplete_tables_info'):
                self.incomplete_tables_info = {}
            self.incomplete_tables_info[base_data.get('id', '')] = incomplete_tables

            return is_complete

        except Exception as e:
            logger.error(f"Error checking base completeness: {str(e)}")
            return False

    def view_base_schema(self):
        """Open detailed view of selected base schema"""
        try:
            if not hasattr(self, 'selected_base_data'):
                messagebox.showwarning("No Selection", "Please select a base first")
                return

            # Create new window for schema view
            schema_window = tk.Toplevel(self.root)
            schema_window.title(f"Schema: {self.selected_base_data['name']}")
            schema_window.geometry("800x600")

            # Create notebook for tables
            schema_notebook = ttk.Notebook(schema_window)
            schema_notebook.pack(fill='both', expand=True, padx=10, pady=10)

            for table in self.selected_base_data['tables']:
                # Create tab for each table
                table_frame = ttk.Frame(schema_notebook)
                schema_notebook.add(table_frame, text=table['name'])

                # Table info
                info_frame = ttk.LabelFrame(table_frame, text="Table Information", padding=10)
                info_frame.pack(fill='x', padx=10, pady=5)

                ttk.Label(info_frame, text=f"ID: {table['id']}").pack(anchor='w')
                ttk.Label(info_frame, text=f"Description: {table.get('description', 'No description')}").pack(anchor='w')
                ttk.Label(info_frame, text=f"Fields: {len(table['fields'])}").pack(anchor='w')

                # Fields list
                fields_frame = ttk.LabelFrame(table_frame, text="Fields", padding=10)
                fields_frame.pack(fill='both', expand=True, padx=10, pady=5)

                # Create treeview for fields
                fields_tree = ttk.Treeview(fields_frame, columns=('Type', 'Description'), show='tree headings')
                fields_tree.heading('#0', text='Field Name')
                fields_tree.heading('Type', text='Type')
                fields_tree.heading('Description', text='Description')

                for field in table['fields']:
                    fields_tree.insert('', 'end', text=field['name'], values=(
                        field['type'],
                        field.get('description', '')
                    ))

                fields_tree.pack(fill='both', expand=True)

        except Exception as e:
            messagebox.showerror("Error", f"Failed to view schema: {str(e)}")
            logger.error(f"Error viewing base schema: {str(e)}")

    def load_workspaces(self):
        """Load available Airtable workspaces"""
        try:
            success, workspaces = self.airtable_manager.get_workspaces()

            if success:
                if workspaces:
                    workspace_names = ["None (Personal)"] + [ws['name'] for ws in workspaces]
                    self.workspace_combo['values'] = workspace_names

                    # Store workspace data for later use
                    self.workspace_data = {ws['name']: ws['id'] for ws in workspaces}

                    self.log_generation(f"✅ Loaded {len(workspaces)} workspaces")
                else:
                    # No workspaces available or API token doesn't have workspace access
                    self.workspace_combo['values'] = ["None (Personal)"]
                    self.workspace_data = {}
                    self.log_generation("No workspaces available (using personal workspace)")
            else:
                # Fallback to personal workspace only
                self.workspace_combo['values'] = ["None (Personal)"]
                self.workspace_data = {}
                self.log_generation(f"Workspace loading failed, using personal workspace: {workspaces}")

        except Exception as e:
            # Fallback to personal workspace only
            self.workspace_combo['values'] = ["None (Personal)"]
            self.workspace_data = {}
            self.log_generation(f"Error loading workspaces, using personal workspace: {str(e)}")
            logger.error(f"Error loading workspaces: {str(e)}")

    def show_token_help(self):
        """Show help dialog for creating proper API token"""
        help_text = self.airtable_manager.get_token_requirements_help()

        # Create a new window for the help
        help_window = tk.Toplevel(self.root)
        help_window.title("Airtable API Token Requirements")
        help_window.geometry("600x500")
        help_window.transient(self.root)
        help_window.grab_set()

        # Create scrollable text widget
        frame = ttk.Frame(help_window)
        frame.pack(fill='both', expand=True, padx=10, pady=10)

        text_widget = tk.Text(frame, wrap='word', font=('Consolas', 10))
        scrollbar = ttk.Scrollbar(frame, orient='vertical', command=text_widget.yview)
        text_widget.configure(yscrollcommand=scrollbar.set)

        text_widget.pack(side='left', fill='both', expand=True)
        scrollbar.pack(side='right', fill='y')

        # Insert help text
        text_widget.insert('1.0', help_text)
        text_widget.config(state='disabled')

        # Add close button
        button_frame = ttk.Frame(help_window)
        button_frame.pack(fill='x', padx=10, pady=(0, 10))

        ttk.Button(button_frame, text="Close",
                  command=help_window.destroy).pack(side='right')
        ttk.Button(button_frame, text="Open Airtable Token Page",
                  command=lambda: self.open_url("https://airtable.com/create/tokens")).pack(side='right', padx=(0, 10))

    def open_url(self, url):
        """Open URL in default browser"""
        import webbrowser
        webbrowser.open(url)

    def check_and_configure_base(self):
        """Check the selected base structure and add missing Attribution Dashboard tables"""
        try:
            if not hasattr(self, 'selected_base_data') or not self.selected_base_data:
                messagebox.showwarning("No Base Selected", "Please select a base from the list above")
                return

            base_info = self.selected_base_data
            base_id = base_info['id']
            base_name = base_info['name']

            self.log_generation(f"Checking base structure: '{base_name}'")

            # Get current tables in the base
            success, current_tables = self.airtable_manager.get_base_schema(base_id)
            if not success:
                messagebox.showerror("Error", f"Failed to get base schema: {current_tables}")
                return

            # Extract table names
            existing_table_names = [table['name'].lower() for table in current_tables]
            self.log_generation(f"Found {len(current_tables)} existing tables")

            # Get required tables configuration
            required_tables = self._get_required_tables_config()

            # Check which tables are missing
            missing_tables = []
            existing_tables = []

            for table_type, table_config in required_tables.items():
                found = False
                matched_table_name = None

                # First check for exact matches (our created tables)
                if 'exact_names' in table_config:
                    for exact_name in table_config['exact_names']:
                        for table in current_tables:
                            if table['name'] == exact_name:
                                found = True
                                matched_table_name = table['name']
                                existing_tables.append(f"{table_config['default_name']} (exact: {matched_table_name})")
                                break
                        if found:
                            break

                # If not found, check for partial matches (legacy tables)
                if not found:
                    for check_name in table_config['check_names']:
                        if any(check_name in existing_name for existing_name in existing_table_names):
                            # Find the actual table name that matched
                            for table in current_tables:
                                if check_name in table['name'].lower():
                                    matched_table_name = table['name']
                                    break
                            existing_tables.append(f"{table_config['default_name']} (partial: {matched_table_name})")
                            found = True
                            break

                if not found:
                    missing_tables.append(table_config)

            # Report findings
            if existing_tables:
                self.log_generation("Existing Attribution Dashboard tables:")
                for table in existing_tables:
                    self.log_generation(f"  ✓ {table}")

            if missing_tables:
                self.log_generation(f"Missing tables: {len(missing_tables)}")

                # Ask user if they want to create missing tables
                create_msg = f"Found {len(existing_tables)} existing tables.\n\n"
                create_msg += f"Missing {len(missing_tables)} tables:\n"
                for table in missing_tables:
                    create_msg += f"• {table['default_name']}\n"
                create_msg += "\nWould you like to create the missing tables?"

                if messagebox.askyesno("Create Missing Tables?", create_msg):
                    self._create_missing_tables(base_id, missing_tables)
            else:
                self.log_generation("✓ All required Attribution Dashboard tables are present!")
                messagebox.showinfo("Complete", "This base already has all required Attribution Dashboard tables.")

            # Generate configuration regardless
            self.generate_client_config(base_info, base_name)

        except Exception as e:
            error_msg = f"Error checking base: {str(e)}"
            self.log_generation(f"Error: {error_msg}")
            messagebox.showerror("Error", error_msg)
            logger.error(error_msg)

    def _create_missing_tables(self, base_id, missing_tables):
        """Create missing tables in the base"""
        tables_created = []

        self.log_generation(f"Starting table creation for base {base_id}")
        self.log_generation(f"Tables to create: {[t['default_name'] for t in missing_tables]}")

        for table_config in missing_tables:
            try:
                table_template = table_config['template'].copy()
                table_template['name'] = table_config['default_name']

                self.log_generation(f"Creating table: {table_config['default_name']}")
                self.log_generation(f"Template fields: {len(table_template.get('fields', []))}")

                success, result = self.airtable_manager.create_table_in_base(base_id, table_template)

                if success:
                    tables_created.append(table_config['default_name'])
                    self.log_generation(f"✓ Created: {table_config['default_name']}")
                    self.log_generation(f"  Table ID: {result.get('id', 'Unknown')}")
                else:
                    self.log_generation(f"✗ Failed to create {table_config['default_name']}")
                    self.log_generation(f"  Error details: {result}")

            except Exception as e:
                self.log_generation(f"✗ Exception creating {table_config['default_name']}: {str(e)}")
                logger.error(f"Exception creating table {table_config['default_name']}: {str(e)}")
                continue

        self.log_generation(f"Table creation complete. Created {len(tables_created)} tables.")

        if tables_created:
            # Refresh bases list to show new tables
            self.refresh_airtable_bases()

            success_msg = f"Successfully created {len(tables_created)} tables:\n\n"
            success_msg += "\n".join(f"• {table}" for table in tables_created)
            messagebox.showinfo("Tables Created", success_msg)
            self.log_generation(f"Created {len(tables_created)} tables successfully")
        else:
            messagebox.showwarning("No Tables Created", "No tables were successfully created. Check the logs for details.")

        return tables_created



    def generate_client_config(self, base_info, base_name):
        """Generate client configuration from base information"""
        try:
            # Find all data source tables
            ghl_table = None
            google_ads_table = None
            pos_table = None
            meta_ads_table = None

            for table in base_info['tables']:
                table_name_lower = table['name'].lower()
                if 'ghl' in table_name_lower or 'lead' in table_name_lower:
                    ghl_table = table
                elif 'google' in table_name_lower and 'ads' in table_name_lower:
                    google_ads_table = table
                elif 'pos' in table_name_lower or 'sales' in table_name_lower:
                    pos_table = table
                elif 'meta' in table_name_lower or 'facebook' in table_name_lower:
                    meta_ads_table = table

            # Generate configuration
            config = {
                "client_info": {
                    "client_id": base_name.lower().replace(' ', '_'),
                    "business_name": base_name,
                    "created_date": datetime.now().isoformat(),
                    "last_updated": datetime.now().isoformat()
                },
                "data_sources": {
                    "enabled_sources": [],
                    "disabled_sources": ["pos", "meta_ads", "meta_ads_summary", "meta_ads_simplified"],
                    "source_configs": {}
                },
                "tab_configuration": {
                    "enabled_tabs": ["overview"],
                    "tab_order": ["overview"],
                    "default_tab": "overview",
                    "tab_labels": {
                        "overview": "Master Overview"
                    }
                },
                "ui_configuration": {
                    "hide_disabled_features": True,
                    "show_data_source_indicators": False,
                    "simplified_navigation": True
                },
                "performance_settings": {
                    "lazy_load_tabs": True,
                    "preload_data": [],
                    "cache_strategy": "moderate"
                },
                "airtable_configuration": {
                    "base_id": base_info['id'],
                    "api_key_env_var": "AIRTABLE_API_KEY"
                }
            }

            # Add GHL configuration if table found
            if ghl_table:
                config["data_sources"]["enabled_sources"].append("ghl")
                config["data_sources"]["source_configs"]["ghl"] = {
                    "table_id": ghl_table['id'],
                    "cache_ttl": 300,
                    "date_field": "Date Created"
                }
                config["tab_configuration"]["enabled_tabs"].append("ghl")
                config["tab_configuration"]["tab_order"].append("ghl")
                config["tab_configuration"]["tab_labels"]["ghl"] = "GHL Analytics"
                config["performance_settings"]["preload_data"].append("ghl")

            # Add Google Ads configuration if table found
            if google_ads_table:
                config["data_sources"]["enabled_sources"].append("google_ads")
                config["data_sources"]["source_configs"]["google_ads"] = {
                    "table_id": google_ads_table['id'],
                    "cache_ttl": 300,
                    "date_field": "Date"
                }
                config["tab_configuration"]["enabled_tabs"].append("google_ads")
                config["tab_configuration"]["tab_order"].append("google_ads")
                config["tab_configuration"]["tab_labels"]["google_ads"] = "Google Ads"

            # Add POS configuration if table found
            if pos_table:
                config["data_sources"]["enabled_sources"].append("pos")
                config["data_sources"]["source_configs"]["pos"] = {
                    "table_id": pos_table['id'],
                    "cache_ttl": 300,
                    "date_field": "Created"
                }
                config["tab_configuration"]["enabled_tabs"].append("pos")
                config["tab_configuration"]["tab_order"].append("pos")
                config["tab_configuration"]["tab_labels"]["pos"] = "POS Analytics"

            # Add Meta Ads configuration if table found
            if meta_ads_table:
                config["data_sources"]["enabled_sources"].append("meta_ads")
                config["data_sources"]["source_configs"]["meta_ads"] = {
                    "table_id": meta_ads_table['id'],
                    "cache_ttl": 300,
                    "date_field": "Reporting ends"
                }
                config["tab_configuration"]["enabled_tabs"].append("meta_ads")
                config["tab_configuration"]["tab_order"].append("meta_ads")
                config["tab_configuration"]["tab_labels"]["meta_ads"] = "Meta Ads"

            # Display configuration
            config_json = json.dumps(config, indent=2)
            self.airtable_config_preview.delete(1.0, tk.END)
            self.airtable_config_preview.insert(1.0, config_json)

            # Store for later use
            self.generated_config = config

        except Exception as e:
            logger.error(f"Error generating client config: {str(e)}")
            raise

    def copy_airtable_config(self):
        """Copy the generated configuration to clipboard"""
        try:
            config_text = self.airtable_config_preview.get(1.0, tk.END).strip()
            if config_text:
                self.root.clipboard_clear()
                self.root.clipboard_append(config_text)
                self.log_generation("📋 Configuration copied to clipboard")
            else:
                messagebox.showwarning("No Configuration", "No configuration to copy")
        except Exception as e:
            messagebox.showerror("Error", f"Failed to copy configuration: {str(e)}")

    def save_airtable_config(self):
        """Save the generated configuration to client_config.json"""
        try:
            if not hasattr(self, 'generated_config'):
                messagebox.showwarning("No Configuration", "No configuration to save")
                return

            # Save to client_config.json
            with open('client_config.json', 'w', encoding='utf-8') as f:
                json.dump(self.generated_config, f, indent=2)

            self.log_generation("💾 Configuration saved to client_config.json")
            messagebox.showinfo("Success", "Configuration saved to client_config.json")

        except Exception as e:
            error_msg = f"Failed to save configuration: {str(e)}"
            self.log_generation(f"❌ {error_msg}")
            messagebox.showerror("Error", error_msg)

    def auto_configure_client(self):
        """Automatically configure client using the generated config"""
        try:
            if not hasattr(self, 'generated_config'):
                messagebox.showwarning("No Configuration", "No configuration to apply")
                return

            # Update the main configuration with generated config
            config = self.generated_config

            # Update business name
            self.business_name_var.set(config['client_info']['business_name'])

            # Update Airtable base ID
            self.airtable_base_var.set(config['airtable_configuration']['base_id'])

            # Update data sources
            enabled_sources = config['data_sources']['enabled_sources']

            # Update data source checkboxes
            for source_id, var in self.data_source_vars.items():
                var.set(source_id in enabled_sources)

            # Update tab checkboxes
            enabled_tabs = config['tab_configuration']['enabled_tabs']
            for tab_id, var in self.tab_vars.items():
                var.set(tab_id in enabled_tabs)

            # Update configuration preview
            self.update_config_preview()

            self.log_generation("🔄 Auto-configuration applied successfully")
            messagebox.showinfo("Success", "Configuration applied to customizer")

        except Exception as e:
            error_msg = f"Failed to auto-configure: {str(e)}"
            self.log_generation(f"❌ {error_msg}")
            messagebox.showerror("Error", error_msg)

    def run(self):
        """Run the GUI application with proper cleanup"""
        try:
            # Load initial logs
            self.refresh_logs()

            # Setup cleanup on window close
            self.root.protocol("WM_DELETE_WINDOW", self.on_application_close)

            # Start the main loop
            self.root.mainloop()

        except Exception as e:
            logger.error(f"Application error: {str(e)}")
            messagebox.showerror("Application Error", f"An error occurred: {str(e)}")
        finally:
            # Ensure cleanup happens
            self.cleanup_on_exit()

    def on_application_close(self):
        """Handle application close with cleanup"""
        try:
            # Check if web preview is running
            if hasattr(self, 'web_server') and self.web_server:
                response = messagebox.askyesnocancel(
                    "Close Application",
                    "Web preview server is still running.\n\n"
                    "Do you want to stop the server and close the application?\n\n"
                    "• Yes: Stop server and close\n"
                    "• No: Close anyway (server will stop)\n"
                    "• Cancel: Keep application open"
                )

                if response is None:  # Cancel
                    return
                elif response:  # Yes - stop server first
                    self.cleanup_web_preview_on_exit()
                # No - just close (cleanup will happen anyway)

            # Close application
            self.root.destroy()

        except Exception as e:
            logger.error(f"Error closing application: {e}")
            # Force close
            try:
                self.root.destroy()
            except:
                pass

    def cleanup_on_exit(self):
        """Cleanup resources when application exits"""
        try:
            # Cleanup web preview if running
            self.cleanup_web_preview_on_exit()

            # Restore original directory if changed
            if hasattr(self, 'original_dir'):
                try:
                    os.chdir(self.original_dir)
                except:
                    pass

            logger.info("Application cleanup completed")

        except Exception as e:
            logger.warning(f"Error during application cleanup: {e}")

def main():
    """Main entry point"""
    try:
        # Create and run the GUI application
        app = DashboardCustomizerGUI()
        app.run()

    except Exception as e:
        print(f"Failed to start application: {str(e)}")
        logger.error(f"Failed to start application: {str(e)}")

if __name__ == "__main__":
    main()