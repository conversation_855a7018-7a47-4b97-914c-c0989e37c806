2025-07-08 17:01:16,576 - INFO - Codebase analysis completed for C:/Users/<USER>/Downloads/RL Quickfix 6.0
2025-07-08 17:03:31,499 - INFO - Successfully customized dashboard for Owls Repair
2025-07-08 17:04:42,347 - INFO - Successfully customized dashboard for Owls Repair
2025-07-08 17:07:57,091 - INFO - Successfully customized dashboard for Owls Repair
2025-07-08 17:27:38,918 - INFO - Application cleanup completed
2025-07-09 13:21:39,054 - ERROR - Airtable connection test failed: 'DashboardCustomizerGUI' object has no attribute 'log_to_generation'
2025-07-09 13:21:47,730 - INFO - Application cleanup completed
2025-07-09 13:23:36,584 - ERROR - Airtable connection test failed: 'DashboardCustomizerGUI' object has no attribute 'log_to_generation'
2025-07-09 13:26:21,924 - INFO - Application cleanup completed
2025-07-09 13:34:33,534 - INFO - Application cleanup completed
2025-07-09 13:34:36,117 - INFO - Application cleanup completed
2025-07-09 13:34:50,332 - INFO - Found 4 accessible Airtable bases
2025-07-09 13:35:13,946 - ERROR - Base creation failed (422): {"error":{"type":"INVALID_REQUEST","message":"Server error"}}
2025-07-09 13:45:49,826 - INFO - Application cleanup completed
2025-07-09 13:45:50,701 - INFO - Application cleanup completed
2025-07-09 13:51:44,463 - INFO - Application cleanup completed
2025-07-09 13:51:58,275 - INFO - Found 4 accessible Airtable bases
2025-07-09 13:52:07,682 - INFO - Retrieved schema for base apptyeo8OtplQ8wAN: 1 tables
2025-07-09 13:53:08,052 - INFO - Created table: iGenius Repair - GHL Leads in base apptyeo8OtplQ8wAN
2025-07-09 13:53:08,732 - INFO - Created table: iGenius Repair - Google Ads in base apptyeo8OtplQ8wAN
2025-07-09 13:53:09,409 - INFO - Created table: iGenius Repair - POS Data in base apptyeo8OtplQ8wAN
2025-07-09 13:53:10,132 - INFO - Created table: iGenius Repair - Meta Ads in base apptyeo8OtplQ8wAN
2025-07-09 13:53:10,719 - INFO - Found 4 accessible Airtable bases
2025-07-09 13:57:28,133 - INFO - Application cleanup completed
2025-07-09 14:00:59,543 - INFO - Application cleanup completed
2025-07-09 14:01:22,982 - INFO - Found 4 accessible Airtable bases
2025-07-09 14:01:26,245 - INFO - Retrieved schema for base apptyeo8OtplQ8wAN: 1 tables
2025-07-09 14:01:31,437 - INFO - Retrieved schema for base apptyeo8OtplQ8wAN: 1 tables
2025-07-09 14:01:39,867 - INFO - Created table: Google Ads in base apptyeo8OtplQ8wAN
2025-07-09 14:01:40,605 - INFO - Created table: POS in base apptyeo8OtplQ8wAN
2025-07-09 14:01:41,402 - INFO - Created table: Meta Ads in base apptyeo8OtplQ8wAN
2025-07-09 14:01:42,917 - INFO - Found 4 accessible Airtable bases
2025-07-09 14:09:58,184 - INFO - Retrieved schema for base appHschkYyi0nRLZR: 1 tables
2025-07-09 14:10:03,676 - INFO - Application cleanup completed
2025-07-09 14:10:32,010 - INFO - Found 4 accessible Airtable bases
2025-07-09 14:10:53,353 - INFO - Retrieved schema for base appHschkYyi0nRLZR: 1 tables
2025-07-09 14:11:01,192 - INFO - Retrieved schema for base appHschkYyi0nRLZR: 1 tables
2025-07-09 14:11:24,752 - INFO - Created table: Google Ads in base appHschkYyi0nRLZR
2025-07-09 14:11:25,480 - INFO - Created table: POS in base appHschkYyi0nRLZR
2025-07-09 14:11:26,166 - INFO - Created table: Meta Ads in base appHschkYyi0nRLZR
2025-07-09 14:11:26,791 - INFO - Found 4 accessible Airtable bases
2025-07-09 14:14:26,625 - INFO - Application cleanup completed
2025-07-09 14:31:27,166 - INFO - Found 8 accessible Airtable bases
2025-07-09 14:40:04,513 - ERROR - Failed to start application: ['text'] are not supported arguments. Look at the documentation for supported arguments.
2025-07-09 14:40:40,583 - ERROR - Failed to start application: ['text'] are not supported arguments. Look at the documentation for supported arguments.
2025-07-09 14:41:15,596 - ERROR - Failed to start application: ['text'] are not supported arguments. Look at the documentation for supported arguments.
2025-07-09 14:42:04,135 - ERROR - Failed to start application: ['text'] are not supported arguments. Look at the documentation for supported arguments.
2025-07-09 14:42:50,779 - ERROR - Failed to start application: ['text'] are not supported arguments. Look at the documentation for supported arguments.
2025-07-09 14:43:17,982 - ERROR - Failed to start application: ['text'] are not supported arguments. Look at the documentation for supported arguments.
2025-07-09 14:45:20,552 - ERROR - Failed to start application: ['text'] are not supported arguments. Look at the documentation for supported arguments.
2025-07-09 14:48:45,958 - ERROR - Failed to start application: ['text'] are not supported arguments. Look at the documentation for supported arguments.
2025-07-09 14:50:44,197 - ERROR - Failed to start application: ['text'] are not supported arguments. Look at the documentation for supported arguments.
2025-07-09 14:52:24,453 - ERROR - Failed to start application: ['text'] are not supported arguments. Look at the documentation for supported arguments.
2025-07-09 14:53:40,685 - ERROR - Failed to start application: ['text'] are not supported arguments. Look at the documentation for supported arguments.
2025-07-09 15:00:57,636 - ERROR - Failed to start application: ['text'] are not supported arguments. Look at the documentation for supported arguments.
2025-07-09 15:06:13,955 - ERROR - Failed to start application: ['text'] are not supported arguments. Look at the documentation for supported arguments.
2025-07-09 15:06:29,824 - ERROR - Failed to start application: ['text'] are not supported arguments. Look at the documentation for supported arguments.
2025-07-09 15:06:55,009 - ERROR - Failed to start application: ['text'] are not supported arguments. Look at the documentation for supported arguments.
2025-07-09 15:07:18,726 - ERROR - Failed to start application: cannot use geometry manager grid inside .!ctktabview.!ctkframe2.!ctkframe.!canvas.!ctkscrollableframe.!ctkframe which already has slaves managed by pack
2025-07-09 15:07:58,518 - ERROR - Failed to start application: cannot use geometry manager grid inside .!ctktabview.!ctkframe2.!ctkframe.!canvas.!ctkscrollableframe.!ctkframe which already has slaves managed by pack
2025-07-09 15:08:28,714 - ERROR - Failed to start application: cannot use geometry manager grid inside .!ctktabview.!ctkframe2.!ctkframe.!canvas.!ctkscrollableframe.!ctkframe which already has slaves managed by pack
2025-07-09 15:09:33,866 - INFO - Application cleanup completed
2025-07-09 15:10:06,920 - ERROR - Failed to start application: cannot use geometry manager grid inside .!ctktabview.!ctkframe2.!ctkframe.!canvas.!ctkscrollableframe.!ctkframe which already has slaves managed by pack
2025-07-09 15:10:51,702 - ERROR - Failed to start application: cannot use geometry manager grid inside .!ctktabview.!ctkframe2.!ctkframe.!canvas.!ctkscrollableframe.!ctkframe which already has slaves managed by pack
2025-07-09 15:11:01,287 - ERROR - Failed to start application: cannot use geometry manager grid inside .!ctktabview.!ctkframe2.!ctkframe.!canvas.!ctkscrollableframe.!ctkframe which already has slaves managed by pack
2025-07-09 15:11:37,923 - ERROR - Failed to start application: cannot use geometry manager grid inside .!ctktabview.!ctkframe2.!ctkframe.!canvas.!ctkscrollableframe.!ctkframe which already has slaves managed by pack
2025-07-09 15:12:29,241 - ERROR - Failed to start application: cannot use geometry manager grid inside .!ctktabview.!ctkframe2.!ctkframe.!canvas.!ctkscrollableframe.!ctkframe which already has slaves managed by pack
2025-07-09 15:13:57,547 - INFO - Application cleanup completed
2025-07-09 15:21:17,654 - INFO - Application cleanup completed
2025-07-09 15:25:44,221 - INFO - Application cleanup completed
2025-07-09 15:25:45,009 - INFO - Application cleanup completed
2025-07-09 15:31:47,511 - INFO - Application cleanup completed
2025-07-09 15:45:00,903 - INFO - Application cleanup completed
2025-07-09 15:45:12,389 - INFO - Found 8 accessible Airtable bases
2025-07-09 15:45:19,665 - INFO - Retrieved schema for base app7ffftdM6e3yekG: 11 tables
2025-07-09 15:45:21,629 - INFO - Retrieved schema for base appHschkYyi0nRLZR: 4 tables
2025-07-09 15:45:23,672 - INFO - Retrieved schema for base app7wu1MtoDXK0UBN: 1 tables
2025-07-09 15:45:25,821 - INFO - Retrieved schema for base apptyeo8OtplQ8wAN: 4 tables
2025-07-09 15:45:27,442 - INFO - Retrieved schema for base appJWDQxzECcXvWz7: 1 tables
2025-07-09 15:45:29,049 - INFO - Retrieved schema for base app5ldOYdhNAOaeOF: 1 tables
2025-07-09 15:45:30,955 - INFO - Retrieved schema for base app9JgRBZC2GNlaKM: 1 tables
2025-07-09 15:45:36,128 - INFO - Retrieved schema for base appL4rTljQgGkjTtp: 1 tables
2025-07-09 15:45:57,970 - INFO - Found 8 accessible Airtable bases
2025-07-09 15:45:58,694 - INFO - Retrieved schema for base app7ffftdM6e3yekG: 11 tables
2025-07-09 15:45:59,343 - INFO - Retrieved schema for base appHschkYyi0nRLZR: 4 tables
2025-07-09 15:46:00,046 - INFO - Retrieved schema for base app7wu1MtoDXK0UBN: 1 tables
2025-07-09 15:46:00,690 - INFO - Retrieved schema for base apptyeo8OtplQ8wAN: 4 tables
2025-07-09 15:46:01,452 - INFO - Retrieved schema for base appJWDQxzECcXvWz7: 1 tables
2025-07-09 15:46:02,115 - INFO - Retrieved schema for base app5ldOYdhNAOaeOF: 1 tables
2025-07-09 15:46:02,804 - INFO - Retrieved schema for base app9JgRBZC2GNlaKM: 1 tables
2025-07-09 15:46:03,431 - INFO - Retrieved schema for base appL4rTljQgGkjTtp: 1 tables
2025-07-09 15:50:34,862 - INFO - Application cleanup completed
2025-07-09 15:50:47,212 - INFO - Found 8 accessible Airtable bases
2025-07-09 15:50:47,967 - INFO - Retrieved schema for base app7ffftdM6e3yekG: 8 tables
2025-07-09 15:50:49,966 - INFO - Retrieved schema for base appHschkYyi0nRLZR: 4 tables
2025-07-09 15:50:51,916 - INFO - Retrieved schema for base app7wu1MtoDXK0UBN: 1 tables
2025-07-09 15:50:53,997 - INFO - Retrieved schema for base apptyeo8OtplQ8wAN: 4 tables
2025-07-09 15:50:55,674 - INFO - Retrieved schema for base appJWDQxzECcXvWz7: 1 tables
2025-07-09 15:50:57,225 - INFO - Retrieved schema for base app5ldOYdhNAOaeOF: 1 tables
2025-07-09 15:50:58,858 - INFO - Retrieved schema for base app9JgRBZC2GNlaKM: 1 tables
2025-07-09 15:51:00,471 - INFO - Retrieved schema for base appL4rTljQgGkjTtp: 1 tables
2025-07-09 15:51:13,267 - INFO - Retrieved schema for base app7wu1MtoDXK0UBN: 1 tables
2025-07-09 15:51:14,086 - INFO - Retrieved schema for base app7wu1MtoDXK0UBN: 1 tables
2025-07-09 15:51:23,693 - INFO - Retrieved schema for base app7wu1MtoDXK0UBN: 1 tables
2025-07-09 15:51:26,305 - INFO - Retrieved schema for base app7wu1MtoDXK0UBN: 1 tables
2025-07-09 15:51:30,824 - ERROR - Error analyzing base Owl Repairs: 'DashboardCustomizerGUI' object has no attribute '_get_required_tables_config'
2025-07-09 15:52:06,413 - INFO - Retrieved schema for base appJWDQxzECcXvWz7: 1 tables
2025-07-09 15:52:15,195 - INFO - Retrieved schema for base app5ldOYdhNAOaeOF: 1 tables
2025-07-09 15:52:26,428 - ERROR - Error analyzing base Owl Repairs: 'DashboardCustomizerGUI' object has no attribute '_get_required_tables_config'
2025-07-09 15:52:26,432 - ERROR - Error analyzing base Ready Set Repair: 'DashboardCustomizerGUI' object has no attribute '_get_required_tables_config'
2025-07-09 15:52:26,437 - ERROR - Error analyzing base MobileComm: 'DashboardCustomizerGUI' object has no attribute '_get_required_tables_config'
2025-07-09 15:52:50,351 - INFO - Found 8 accessible Airtable bases
2025-07-09 15:52:51,086 - INFO - Retrieved schema for base app7ffftdM6e3yekG: 8 tables
2025-07-09 15:52:51,736 - INFO - Retrieved schema for base appHschkYyi0nRLZR: 4 tables
2025-07-09 15:52:52,373 - INFO - Retrieved schema for base app7wu1MtoDXK0UBN: 1 tables
2025-07-09 15:52:53,014 - INFO - Retrieved schema for base apptyeo8OtplQ8wAN: 4 tables
2025-07-09 15:52:53,642 - INFO - Retrieved schema for base appJWDQxzECcXvWz7: 1 tables
2025-07-09 15:52:54,282 - INFO - Retrieved schema for base app5ldOYdhNAOaeOF: 1 tables
2025-07-09 15:52:54,983 - INFO - Retrieved schema for base app9JgRBZC2GNlaKM: 1 tables
2025-07-09 15:52:55,602 - INFO - Retrieved schema for base appL4rTljQgGkjTtp: 1 tables
2025-07-09 15:53:07,179 - INFO - Retrieved schema for base app7wu1MtoDXK0UBN: 1 tables
2025-07-09 15:53:10,744 - ERROR - Error analyzing base Owl Repairs: 'DashboardCustomizerGUI' object has no attribute '_get_required_tables_config'
2025-07-09 15:53:18,191 - ERROR - Error analyzing base Owl Repairs: 'DashboardCustomizerGUI' object has no attribute '_get_required_tables_config'
2025-07-09 15:56:44,223 - INFO - Application cleanup completed
2025-07-09 15:57:40,571 - INFO - Application cleanup completed
2025-07-09 15:58:13,495 - INFO - Application cleanup completed
2025-07-09 15:58:45,284 - INFO - Found 8 accessible Airtable bases
2025-07-09 15:58:46,050 - INFO - Retrieved schema for base app7ffftdM6e3yekG: 8 tables
2025-07-09 15:58:47,923 - INFO - Retrieved schema for base appHschkYyi0nRLZR: 4 tables
2025-07-09 15:58:49,689 - INFO - Retrieved schema for base app7wu1MtoDXK0UBN: 1 tables
2025-07-09 15:58:51,713 - INFO - Retrieved schema for base apptyeo8OtplQ8wAN: 4 tables
2025-07-09 15:58:53,285 - INFO - Retrieved schema for base appJWDQxzECcXvWz7: 1 tables
2025-07-09 15:58:54,896 - INFO - Retrieved schema for base app5ldOYdhNAOaeOF: 1 tables
2025-07-09 15:58:56,613 - INFO - Retrieved schema for base app9JgRBZC2GNlaKM: 1 tables
2025-07-09 15:58:58,259 - INFO - Retrieved schema for base appL4rTljQgGkjTtp: 1 tables
2025-07-09 15:59:02,527 - INFO - Retrieved schema for base app7wu1MtoDXK0UBN: 1 tables
2025-07-09 15:59:20,498 - INFO - Creating table 'Google Ads Data' in base app7wu1MtoDXK0UBN
2025-07-09 15:59:20,499 - INFO - Template has 11 fields
2025-07-09 15:59:21,408 - INFO - Created table: Google Ads Data in base app7wu1MtoDXK0UBN
2025-07-09 15:59:21,409 - INFO - Successfully created table 'Google Ads Data'
2025-07-09 15:59:21,419 - INFO - Creating table 'POS Sales Data' in base app7wu1MtoDXK0UBN
2025-07-09 15:59:21,419 - INFO - Template has 10 fields
2025-07-09 15:59:22,154 - INFO - Created table: POS Sales Data in base app7wu1MtoDXK0UBN
2025-07-09 15:59:22,156 - INFO - Successfully created table 'POS Sales Data'
2025-07-09 15:59:22,167 - INFO - Creating table 'Meta Ads Data' in base app7wu1MtoDXK0UBN
2025-07-09 15:59:22,167 - INFO - Template has 14 fields
2025-07-09 15:59:22,878 - INFO - Created table: Meta Ads Data in base app7wu1MtoDXK0UBN
2025-07-09 15:59:22,880 - INFO - Successfully created table 'Meta Ads Data'
2025-07-09 16:05:26,769 - INFO - Found 8 accessible Airtable bases
2025-07-09 16:05:27,517 - INFO - Retrieved schema for base app7ffftdM6e3yekG: 8 tables
2025-07-09 16:05:29,418 - INFO - Retrieved schema for base appHschkYyi0nRLZR: 4 tables
2025-07-09 16:05:30,098 - INFO - Retrieved schema for base app7wu1MtoDXK0UBN: 4 tables
2025-07-09 16:05:32,311 - INFO - Retrieved schema for base apptyeo8OtplQ8wAN: 4 tables
2025-07-09 16:05:33,937 - INFO - Retrieved schema for base appJWDQxzECcXvWz7: 1 tables
2025-07-09 16:05:35,617 - INFO - Retrieved schema for base app5ldOYdhNAOaeOF: 1 tables
2025-07-09 16:05:37,313 - INFO - Retrieved schema for base app9JgRBZC2GNlaKM: 1 tables
2025-07-09 16:05:38,939 - INFO - Retrieved schema for base appL4rTljQgGkjTtp: 1 tables
2025-07-09 16:05:46,049 - INFO - Retrieved schema for base appJWDQxzECcXvWz7: 1 tables
2025-07-09 16:05:51,354 - INFO - Retrieved schema for base app5ldOYdhNAOaeOF: 1 tables
2025-07-09 16:06:05,378 - INFO - Creating table 'GHL' in base appJWDQxzECcXvWz7
2025-07-09 16:06:05,378 - INFO - Template has 8 fields
2025-07-09 16:06:06,482 - INFO - Created table: GHL in base appJWDQxzECcXvWz7
2025-07-09 16:06:06,484 - INFO - Successfully created table 'GHL'
2025-07-09 16:06:06,494 - INFO - Creating table 'Google Ads' in base appJWDQxzECcXvWz7
2025-07-09 16:06:06,494 - INFO - Template has 11 fields
2025-07-09 16:06:07,160 - INFO - Created table: Google Ads in base appJWDQxzECcXvWz7
2025-07-09 16:06:07,162 - INFO - Successfully created table 'Google Ads'
2025-07-09 16:06:07,177 - INFO - Creating table 'POS' in base appJWDQxzECcXvWz7
2025-07-09 16:06:07,177 - INFO - Template has 10 fields
2025-07-09 16:06:07,875 - INFO - Created table: POS in base appJWDQxzECcXvWz7
2025-07-09 16:06:07,877 - INFO - Successfully created table 'POS'
2025-07-09 16:06:07,890 - INFO - Creating table 'Meta Ads' in base appJWDQxzECcXvWz7
2025-07-09 16:06:07,891 - INFO - Template has 14 fields
2025-07-09 16:06:08,695 - INFO - Created table: Meta Ads in base appJWDQxzECcXvWz7
2025-07-09 16:06:08,697 - INFO - Successfully created table 'Meta Ads'
2025-07-09 16:06:08,713 - INFO - Creating table 'GHL' in base app5ldOYdhNAOaeOF
2025-07-09 16:06:08,713 - INFO - Template has 8 fields
2025-07-09 16:06:09,578 - INFO - Created table: GHL in base app5ldOYdhNAOaeOF
2025-07-09 16:06:09,580 - INFO - Successfully created table 'GHL'
2025-07-09 16:06:09,592 - INFO - Creating table 'Google Ads' in base app5ldOYdhNAOaeOF
2025-07-09 16:06:09,592 - INFO - Template has 11 fields
2025-07-09 16:06:10,290 - INFO - Created table: Google Ads in base app5ldOYdhNAOaeOF
2025-07-09 16:06:10,292 - INFO - Successfully created table 'Google Ads'
2025-07-09 16:06:10,299 - INFO - Creating table 'POS' in base app5ldOYdhNAOaeOF
2025-07-09 16:06:10,300 - INFO - Template has 10 fields
2025-07-09 16:06:11,009 - INFO - Created table: POS in base app5ldOYdhNAOaeOF
2025-07-09 16:06:11,011 - INFO - Successfully created table 'POS'
2025-07-09 16:06:11,014 - INFO - Creating table 'Meta Ads' in base app5ldOYdhNAOaeOF
2025-07-09 16:06:11,014 - INFO - Template has 14 fields
2025-07-09 16:06:11,718 - INFO - Created table: Meta Ads in base app5ldOYdhNAOaeOF
2025-07-09 16:06:11,719 - INFO - Successfully created table 'Meta Ads'
2025-07-09 16:06:14,369 - INFO - Found 8 accessible Airtable bases
2025-07-09 16:06:15,187 - INFO - Retrieved schema for base app7ffftdM6e3yekG: 8 tables
2025-07-09 16:06:15,937 - INFO - Retrieved schema for base appHschkYyi0nRLZR: 4 tables
2025-07-09 16:06:16,597 - INFO - Retrieved schema for base app7wu1MtoDXK0UBN: 4 tables
2025-07-09 16:06:17,220 - INFO - Retrieved schema for base apptyeo8OtplQ8wAN: 4 tables
2025-07-09 16:06:17,833 - INFO - Retrieved schema for base appJWDQxzECcXvWz7: 5 tables
2025-07-09 16:06:18,461 - INFO - Retrieved schema for base app5ldOYdhNAOaeOF: 5 tables
2025-07-09 16:06:19,143 - INFO - Retrieved schema for base app9JgRBZC2GNlaKM: 1 tables
2025-07-09 16:06:19,778 - INFO - Retrieved schema for base appL4rTljQgGkjTtp: 1 tables
2025-07-09 16:07:55,379 - INFO - Application cleanup completed
2025-07-09 16:11:50,017 - INFO - Found 8 accessible Airtable bases
2025-07-09 16:11:50,760 - INFO - Retrieved schema for base app7ffftdM6e3yekG: 8 tables
2025-07-09 16:11:52,769 - INFO - Retrieved schema for base appHschkYyi0nRLZR: 4 tables
2025-07-09 16:11:54,728 - INFO - Retrieved schema for base app7wu1MtoDXK0UBN: 4 tables
2025-07-09 16:11:56,752 - INFO - Retrieved schema for base apptyeo8OtplQ8wAN: 4 tables
2025-07-09 16:11:58,449 - INFO - Retrieved schema for base appJWDQxzECcXvWz7: 5 tables
2025-07-09 16:12:00,096 - INFO - Retrieved schema for base app5ldOYdhNAOaeOF: 5 tables
2025-07-09 16:12:01,717 - INFO - Retrieved schema for base app9JgRBZC2GNlaKM: 1 tables
2025-07-09 16:12:03,378 - INFO - Retrieved schema for base appL4rTljQgGkjTtp: 1 tables
2025-07-09 16:12:51,946 - INFO - Retrieved schema for base app9JgRBZC2GNlaKM: 1 tables
2025-07-09 16:13:10,848 - INFO - Creating table 'GHL' in base app9JgRBZC2GNlaKM
2025-07-09 16:13:10,849 - INFO - Template has 8 fields
2025-07-09 16:13:11,736 - INFO - Created table: GHL in base app9JgRBZC2GNlaKM
2025-07-09 16:13:11,738 - INFO - Successfully created table 'GHL'
2025-07-09 16:13:11,749 - INFO - Creating table 'Google Ads' in base app9JgRBZC2GNlaKM
2025-07-09 16:13:11,750 - INFO - Template has 11 fields
2025-07-09 16:13:12,519 - INFO - Created table: Google Ads in base app9JgRBZC2GNlaKM
2025-07-09 16:13:12,521 - INFO - Successfully created table 'Google Ads'
2025-07-09 16:13:12,533 - INFO - Creating table 'POS' in base app9JgRBZC2GNlaKM
2025-07-09 16:13:12,533 - INFO - Template has 10 fields
2025-07-09 16:13:13,307 - INFO - Created table: POS in base app9JgRBZC2GNlaKM
2025-07-09 16:13:13,308 - INFO - Successfully created table 'POS'
2025-07-09 16:13:13,327 - INFO - Creating table 'Meta Ads' in base app9JgRBZC2GNlaKM
2025-07-09 16:13:13,328 - INFO - Template has 14 fields
2025-07-09 16:13:14,031 - INFO - Created table: Meta Ads in base app9JgRBZC2GNlaKM
2025-07-09 16:13:14,033 - INFO - Successfully created table 'Meta Ads'
2025-07-09 16:13:16,656 - INFO - Retrieved schema for base app9JgRBZC2GNlaKM: 5 tables
2025-07-09 16:13:16,668 - ERROR - Error updating analysis results: 'DashboardCustomizerGUI' object has no attribute 'analysis_summary_label'
2025-07-09 16:13:37,617 - INFO - Retrieved schema for base appL4rTljQgGkjTtp: 1 tables
2025-07-09 16:13:48,651 - INFO - Creating table 'GHL' in base appL4rTljQgGkjTtp
2025-07-09 16:13:48,652 - INFO - Template has 8 fields
2025-07-09 16:13:49,453 - INFO - Created table: GHL in base appL4rTljQgGkjTtp
2025-07-09 16:13:49,454 - INFO - Successfully created table 'GHL'
2025-07-09 16:13:49,470 - INFO - Creating table 'Google Ads' in base appL4rTljQgGkjTtp
2025-07-09 16:13:49,470 - INFO - Template has 11 fields
2025-07-09 16:13:50,178 - INFO - Created table: Google Ads in base appL4rTljQgGkjTtp
2025-07-09 16:13:50,181 - INFO - Successfully created table 'Google Ads'
2025-07-09 16:13:50,192 - INFO - Creating table 'POS' in base appL4rTljQgGkjTtp
2025-07-09 16:13:50,193 - INFO - Template has 10 fields
2025-07-09 16:13:50,917 - INFO - Created table: POS in base appL4rTljQgGkjTtp
2025-07-09 16:13:50,918 - INFO - Successfully created table 'POS'
2025-07-09 16:13:50,928 - INFO - Creating table 'Meta Ads' in base appL4rTljQgGkjTtp
2025-07-09 16:13:50,928 - INFO - Template has 14 fields
2025-07-09 16:13:51,634 - INFO - Created table: Meta Ads in base appL4rTljQgGkjTtp
2025-07-09 16:13:51,636 - INFO - Successfully created table 'Meta Ads'
2025-07-09 16:13:54,277 - INFO - Retrieved schema for base appL4rTljQgGkjTtp: 5 tables
2025-07-09 16:13:54,290 - ERROR - Error updating analysis results: 'DashboardCustomizerGUI' object has no attribute 'analysis_summary_label'
2025-07-09 16:14:00,099 - INFO - Found 8 accessible Airtable bases
2025-07-09 16:14:00,862 - INFO - Retrieved schema for base app7ffftdM6e3yekG: 8 tables
2025-07-09 16:14:01,551 - INFO - Retrieved schema for base appHschkYyi0nRLZR: 4 tables
2025-07-09 16:14:02,271 - INFO - Retrieved schema for base app7wu1MtoDXK0UBN: 4 tables
2025-07-09 16:14:02,948 - INFO - Retrieved schema for base apptyeo8OtplQ8wAN: 4 tables
2025-07-09 16:14:03,590 - INFO - Retrieved schema for base appJWDQxzECcXvWz7: 4 tables
2025-07-09 16:14:04,217 - INFO - Retrieved schema for base app5ldOYdhNAOaeOF: 4 tables
2025-07-09 16:14:04,857 - INFO - Retrieved schema for base app9JgRBZC2GNlaKM: 5 tables
2025-07-09 16:14:05,513 - INFO - Retrieved schema for base appL4rTljQgGkjTtp: 5 tables
2025-07-09 16:14:48,427 - INFO - Found 8 accessible Airtable bases
2025-07-09 16:14:49,185 - INFO - Retrieved schema for base app7ffftdM6e3yekG: 8 tables
2025-07-09 16:14:49,870 - INFO - Retrieved schema for base appHschkYyi0nRLZR: 4 tables
2025-07-09 16:14:50,537 - INFO - Retrieved schema for base app7wu1MtoDXK0UBN: 4 tables
2025-07-09 16:14:51,237 - INFO - Retrieved schema for base apptyeo8OtplQ8wAN: 4 tables
2025-07-09 16:14:51,904 - INFO - Retrieved schema for base appJWDQxzECcXvWz7: 4 tables
2025-07-09 16:14:52,575 - INFO - Retrieved schema for base app5ldOYdhNAOaeOF: 4 tables
2025-07-09 16:14:53,255 - INFO - Retrieved schema for base app9JgRBZC2GNlaKM: 4 tables
2025-07-09 16:14:53,953 - INFO - Retrieved schema for base appL4rTljQgGkjTtp: 4 tables
2025-07-09 16:18:02,000 - INFO - Found 8 accessible Airtable bases
2025-07-09 16:18:02,790 - INFO - Retrieved schema for base app7ffftdM6e3yekG: 8 tables
2025-07-09 16:18:04,647 - INFO - Retrieved schema for base appHschkYyi0nRLZR: 4 tables
2025-07-09 16:18:06,491 - INFO - Retrieved schema for base app7wu1MtoDXK0UBN: 4 tables
2025-07-09 16:18:08,217 - INFO - Retrieved schema for base apptyeo8OtplQ8wAN: 4 tables
2025-07-09 16:18:09,759 - INFO - Retrieved schema for base appJWDQxzECcXvWz7: 4 tables
2025-07-09 16:18:11,238 - INFO - Retrieved schema for base app5ldOYdhNAOaeOF: 4 tables
2025-07-09 16:18:11,898 - INFO - Retrieved schema for base app9JgRBZC2GNlaKM: 4 tables
2025-07-09 16:18:13,096 - INFO - Retrieved schema for base appL4rTljQgGkjTtp: 4 tables
2025-07-09 16:18:13,097 - INFO - Checking completeness for 'Quick Fix'
2025-07-09 16:18:13,097 - INFO - Existing tables: ['GHL', 'POS', 'Google Ads', 'Meta Ads', 'Meta Ads Summary', 'Meta Ads AT', 'Meta Ads Simplified', 'Meta Ads Performance']
2025-07-09 16:18:13,097 - INFO - Found tables: ['GHL (matched: ghl)', 'Google Ads (matched: google ads)', 'POS (matched: pos)', 'Meta Ads (matched: meta ads)']
2025-07-09 16:18:13,098 - INFO - Missing tables: []
2025-07-09 16:18:13,098 - INFO - Base 'Quick Fix' is complete: True
2025-07-09 16:18:13,098 - INFO - Checking completeness for 'Fix My Gadget'
2025-07-09 16:18:13,098 - INFO - Existing tables: ['GHL', 'Google Ads', 'POS', 'Meta Ads']
2025-07-09 16:18:13,098 - INFO - Found tables: ['GHL (matched: ghl)', 'Google Ads (matched: google ads)', 'POS (matched: pos)', 'Meta Ads (matched: meta ads)']
2025-07-09 16:18:13,098 - INFO - Missing tables: []
2025-07-09 16:18:13,098 - INFO - Base 'Fix My Gadget' is complete: True
2025-07-09 16:18:13,098 - INFO - Checking completeness for 'Owl Repairs'
2025-07-09 16:18:13,099 - INFO - Existing tables: ['GHL', 'Google Ads Data', 'POS Sales Data', 'Meta Ads Data']
2025-07-09 16:18:13,099 - INFO - Found tables: ['GHL (matched: ghl)', 'Google Ads (matched: google ads data)', 'POS (matched: pos sales data)', 'Meta Ads (matched: meta ads data)']
2025-07-09 16:18:13,099 - INFO - Missing tables: []
2025-07-09 16:18:13,099 - INFO - Base 'Owl Repairs' is complete: True
2025-07-09 16:18:13,099 - INFO - Checking completeness for 'iGenius Repair'
2025-07-09 16:18:13,099 - INFO - Existing tables: ['GHL', 'Google Ads', 'POS', 'Meta Ads']
2025-07-09 16:18:13,099 - INFO - Found tables: ['GHL (matched: ghl)', 'Google Ads (matched: google ads)', 'POS (matched: pos)', 'Meta Ads (matched: meta ads)']
2025-07-09 16:18:13,099 - INFO - Missing tables: []
2025-07-09 16:18:13,099 - INFO - Base 'iGenius Repair' is complete: True
2025-07-09 16:18:13,099 - INFO - Checking completeness for 'Ready Set Repair'
2025-07-09 16:18:13,099 - INFO - Existing tables: ['GHL', 'Google Ads', 'POS', 'Meta Ads']
2025-07-09 16:18:13,099 - INFO - Found tables: ['GHL (matched: ghl)', 'Google Ads (matched: google ads)', 'POS (matched: pos)', 'Meta Ads (matched: meta ads)']
2025-07-09 16:18:13,100 - INFO - Missing tables: []
2025-07-09 16:18:13,100 - INFO - Base 'Ready Set Repair' is complete: True
2025-07-09 16:18:13,100 - INFO - Checking completeness for 'MobileComm'
2025-07-09 16:18:13,100 - INFO - Existing tables: ['GHL', 'Google Ads', 'POS', 'Meta Ads']
2025-07-09 16:18:13,100 - INFO - Found tables: ['GHL (matched: ghl)', 'Google Ads (matched: google ads)', 'POS (matched: pos)', 'Meta Ads (matched: meta ads)']
2025-07-09 16:18:13,100 - INFO - Missing tables: []
2025-07-09 16:18:13,100 - INFO - Base 'MobileComm' is complete: True
2025-07-09 16:18:13,100 - INFO - Checking completeness for 'Cellular Zone'
2025-07-09 16:18:13,100 - INFO - Existing tables: ['GHL', 'Google Ads', 'POS', 'Meta Ads']
2025-07-09 16:18:13,100 - INFO - Found tables: ['GHL (matched: ghl)', 'Google Ads (matched: google ads)', 'POS (matched: pos)', 'Meta Ads (matched: meta ads)']
2025-07-09 16:18:13,101 - INFO - Missing tables: []
2025-07-09 16:18:13,101 - INFO - Base 'Cellular Zone' is complete: True
2025-07-09 16:18:13,101 - INFO - Checking completeness for 'Gadget Repair LV'
2025-07-09 16:18:13,101 - INFO - Existing tables: ['GHL', 'Google Ads', 'POS', 'Meta Ads']
2025-07-09 16:18:13,101 - INFO - Found tables: ['GHL (matched: ghl)', 'Google Ads (matched: google ads)', 'POS (matched: pos)', 'Meta Ads (matched: meta ads)']
2025-07-09 16:18:13,101 - INFO - Missing tables: []
2025-07-09 16:18:13,101 - INFO - Base 'Gadget Repair LV' is complete: True
2025-07-09 16:36:28,498 - INFO - Application cleanup completed
2025-07-10 15:09:09,352 - INFO - Found 8 accessible Airtable bases
2025-07-10 15:09:17,882 - INFO - Retrieved schema for base app7ffftdM6e3yekG: 8 tables
2025-07-10 15:09:20,012 - INFO - Retrieved schema for base appHschkYyi0nRLZR: 4 tables
2025-07-10 15:09:22,149 - INFO - Retrieved schema for base app7wu1MtoDXK0UBN: 4 tables
2025-07-10 15:09:25,627 - INFO - Retrieved schema for base apptyeo8OtplQ8wAN: 4 tables
2025-07-10 15:09:27,659 - INFO - Retrieved schema for base appJWDQxzECcXvWz7: 4 tables
2025-07-10 15:09:29,560 - INFO - Retrieved schema for base app5ldOYdhNAOaeOF: 4 tables
2025-07-10 15:09:31,615 - INFO - Retrieved schema for base app9JgRBZC2GNlaKM: 4 tables
2025-07-10 15:09:33,859 - INFO - Retrieved schema for base appL4rTljQgGkjTtp: 4 tables
2025-07-10 15:09:45,304 - INFO - Retrieved schema for base apptyeo8OtplQ8wAN: 4 tables
2025-07-10 15:10:03,370 - INFO - Retrieved schema for base apptyeo8OtplQ8wAN: 4 tables
2025-07-10 15:10:40,281 - INFO - Retrieved schema for base apptyeo8OtplQ8wAN: 4 tables
2025-07-10 15:10:46,215 - INFO - Retrieved schema for base apptyeo8OtplQ8wAN: 4 tables
2025-07-10 15:10:50,743 - INFO - Retrieved schema for base apptyeo8OtplQ8wAN: 4 tables
2025-07-10 15:11:10,140 - INFO - Retrieved schema for base apptyeo8OtplQ8wAN: 4 tables
2025-07-10 15:11:46,536 - INFO - Retrieved schema for base apptyeo8OtplQ8wAN: 4 tables
2025-07-10 15:15:16,781 - INFO - Application cleanup completed
2025-07-10 15:16:46,435 - INFO - Found 8 accessible Airtable bases
2025-07-10 15:16:47,277 - INFO - Retrieved schema for base app7ffftdM6e3yekG: 8 tables
2025-07-10 15:16:49,301 - INFO - Retrieved schema for base appHschkYyi0nRLZR: 4 tables
2025-07-10 15:16:51,360 - INFO - Retrieved schema for base app7wu1MtoDXK0UBN: 4 tables
2025-07-10 15:16:52,016 - INFO - Retrieved schema for base apptyeo8OtplQ8wAN: 4 tables
2025-07-10 15:16:53,934 - INFO - Retrieved schema for base appJWDQxzECcXvWz7: 4 tables
2025-07-10 15:16:58,024 - INFO - Retrieved schema for base app5ldOYdhNAOaeOF: 4 tables
2025-07-10 15:17:00,152 - INFO - Retrieved schema for base app9JgRBZC2GNlaKM: 4 tables
2025-07-10 15:17:02,071 - INFO - Retrieved schema for base appL4rTljQgGkjTtp: 4 tables
2025-07-10 15:17:05,019 - INFO - Retrieved schema for base apptyeo8OtplQ8wAN: 4 tables
2025-07-10 15:17:18,398 - INFO - Retrieved schema for base apptyeo8OtplQ8wAN: 4 tables
2025-07-10 15:21:16,149 - INFO - Application cleanup completed
2025-07-10 15:23:52,049 - INFO - Found 8 accessible Airtable bases
2025-07-10 15:23:52,846 - INFO - Retrieved schema for base app7ffftdM6e3yekG: 8 tables
2025-07-10 15:23:54,883 - INFO - Retrieved schema for base appHschkYyi0nRLZR: 4 tables
2025-07-10 15:23:56,913 - INFO - Retrieved schema for base app7wu1MtoDXK0UBN: 4 tables
2025-07-10 15:23:58,787 - INFO - Retrieved schema for base apptyeo8OtplQ8wAN: 4 tables
2025-07-10 15:24:00,681 - INFO - Retrieved schema for base appJWDQxzECcXvWz7: 4 tables
2025-07-10 15:24:02,675 - INFO - Retrieved schema for base app5ldOYdhNAOaeOF: 4 tables
2025-07-10 15:24:04,833 - INFO - Retrieved schema for base app9JgRBZC2GNlaKM: 4 tables
2025-07-10 15:24:06,917 - INFO - Retrieved schema for base appL4rTljQgGkjTtp: 4 tables
2025-07-10 15:24:23,319 - INFO - Retrieved schema for base apptyeo8OtplQ8wAN: 4 tables
2025-07-10 15:24:25,055 - INFO - Retrieved schema for base apptyeo8OtplQ8wAN: 4 tables
2025-07-10 15:30:56,048 - INFO - Retrieved schema for base apptyeo8OtplQ8wAN: 4 tables
2025-07-10 15:31:20,116 - INFO - Retrieved schema for base apptyeo8OtplQ8wAN: 4 tables
2025-07-10 15:31:33,704 - INFO - Application cleanup completed
2025-07-10 15:32:08,290 - INFO - Application cleanup completed
2025-07-10 15:32:26,919 - INFO - Found 8 accessible Airtable bases
2025-07-10 15:32:27,719 - INFO - Retrieved schema for base app7ffftdM6e3yekG: 8 tables
2025-07-10 15:32:29,691 - INFO - Retrieved schema for base appHschkYyi0nRLZR: 4 tables
2025-07-10 15:32:31,708 - INFO - Retrieved schema for base app7wu1MtoDXK0UBN: 4 tables
2025-07-10 15:32:32,396 - INFO - Retrieved schema for base apptyeo8OtplQ8wAN: 4 tables
2025-07-10 15:32:34,294 - INFO - Retrieved schema for base appJWDQxzECcXvWz7: 4 tables
2025-07-10 15:32:36,089 - INFO - Retrieved schema for base app5ldOYdhNAOaeOF: 4 tables
2025-07-10 15:32:37,938 - INFO - Retrieved schema for base app9JgRBZC2GNlaKM: 4 tables
2025-07-10 15:32:39,926 - INFO - Retrieved schema for base appL4rTljQgGkjTtp: 4 tables
2025-07-10 15:33:22,321 - INFO - Retrieved schema for base apptyeo8OtplQ8wAN: 4 tables
2025-07-10 15:36:46,542 - INFO - Found 8 accessible Airtable bases
2025-07-10 15:36:47,320 - INFO - Retrieved schema for base app7ffftdM6e3yekG: 8 tables
2025-07-10 15:36:49,120 - INFO - Retrieved schema for base appHschkYyi0nRLZR: 4 tables
2025-07-10 15:36:50,965 - INFO - Retrieved schema for base app7wu1MtoDXK0UBN: 4 tables
2025-07-10 15:36:52,701 - INFO - Retrieved schema for base apptyeo8OtplQ8wAN: 4 tables
2025-07-10 15:36:54,448 - INFO - Retrieved schema for base appJWDQxzECcXvWz7: 4 tables
2025-07-10 15:36:56,077 - INFO - Retrieved schema for base app5ldOYdhNAOaeOF: 4 tables
2025-07-10 15:36:59,541 - INFO - Retrieved schema for base app9JgRBZC2GNlaKM: 4 tables
2025-07-10 15:37:01,503 - INFO - Retrieved schema for base appL4rTljQgGkjTtp: 4 tables
2025-07-10 15:37:03,559 - INFO - Retrieved schema for base apptyeo8OtplQ8wAN: 4 tables
2025-07-10 15:37:05,651 - INFO - Retrieved schema for base apptyeo8OtplQ8wAN: 4 tables
2025-07-10 15:37:18,416 - INFO - Retrieved schema for base apptyeo8OtplQ8wAN: 4 tables
2025-07-10 15:37:25,450 - INFO - Retrieved schema for base apptyeo8OtplQ8wAN: 4 tables
2025-07-10 15:37:31,768 - INFO - Retrieved schema for base apptyeo8OtplQ8wAN: 4 tables
2025-07-10 15:37:45,133 - INFO - Application cleanup completed
2025-07-10 15:37:46,302 - INFO - Application cleanup completed
2025-07-10 15:37:57,862 - INFO - Found 8 accessible Airtable bases
2025-07-10 15:37:58,636 - INFO - Retrieved schema for base app7ffftdM6e3yekG: 8 tables
2025-07-10 15:37:59,288 - INFO - Retrieved schema for base appHschkYyi0nRLZR: 4 tables
2025-07-10 15:37:59,952 - INFO - Retrieved schema for base app7wu1MtoDXK0UBN: 4 tables
2025-07-10 15:38:00,626 - INFO - Retrieved schema for base apptyeo8OtplQ8wAN: 4 tables
2025-07-10 15:38:01,285 - INFO - Retrieved schema for base appJWDQxzECcXvWz7: 4 tables
2025-07-10 15:38:01,904 - INFO - Retrieved schema for base app5ldOYdhNAOaeOF: 4 tables
2025-07-10 15:38:02,558 - INFO - Retrieved schema for base app9JgRBZC2GNlaKM: 4 tables
2025-07-10 15:38:03,175 - INFO - Retrieved schema for base appL4rTljQgGkjTtp: 4 tables
2025-07-10 15:38:19,243 - INFO - Retrieved schema for base apptyeo8OtplQ8wAN: 4 tables
2025-07-10 15:38:22,338 - INFO - Retrieved schema for base apptyeo8OtplQ8wAN: 4 tables
2025-07-10 15:41:21,183 - INFO - Found 8 accessible Airtable bases
2025-07-10 15:41:21,954 - INFO - Retrieved schema for base app7ffftdM6e3yekG: 8 tables
2025-07-10 15:41:23,889 - INFO - Retrieved schema for base appHschkYyi0nRLZR: 4 tables
2025-07-10 15:41:25,696 - INFO - Retrieved schema for base app7wu1MtoDXK0UBN: 4 tables
2025-07-10 15:41:30,545 - INFO - Retrieved schema for base apptyeo8OtplQ8wAN: 4 tables
2025-07-10 15:41:32,404 - INFO - Retrieved schema for base appJWDQxzECcXvWz7: 4 tables
2025-07-10 15:41:34,075 - INFO - Retrieved schema for base app5ldOYdhNAOaeOF: 4 tables
2025-07-10 15:41:35,834 - INFO - Retrieved schema for base app9JgRBZC2GNlaKM: 4 tables
2025-07-10 15:41:37,673 - INFO - Retrieved schema for base appL4rTljQgGkjTtp: 4 tables
2025-07-10 15:41:39,880 - INFO - Retrieved schema for base apptyeo8OtplQ8wAN: 4 tables
2025-07-10 15:41:41,539 - INFO - Retrieved schema for base apptyeo8OtplQ8wAN: 4 tables
2025-07-10 15:41:54,608 - INFO - Retrieved schema for base apptyeo8OtplQ8wAN: 4 tables
2025-07-10 15:45:02,876 - INFO - Found 8 accessible Airtable bases
2025-07-10 15:45:03,595 - INFO - Retrieved schema for base app7ffftdM6e3yekG: 8 tables
2025-07-10 15:45:05,478 - INFO - Retrieved schema for base appHschkYyi0nRLZR: 4 tables
2025-07-10 15:45:07,431 - INFO - Retrieved schema for base app7wu1MtoDXK0UBN: 4 tables
2025-07-10 15:45:09,231 - INFO - Retrieved schema for base apptyeo8OtplQ8wAN: 4 tables
2025-07-10 15:45:11,086 - INFO - Retrieved schema for base appJWDQxzECcXvWz7: 4 tables
2025-07-10 15:45:13,621 - INFO - Retrieved schema for base app5ldOYdhNAOaeOF: 4 tables
2025-07-10 15:45:15,426 - INFO - Retrieved schema for base app9JgRBZC2GNlaKM: 4 tables
2025-07-10 15:45:19,817 - INFO - Retrieved schema for base appL4rTljQgGkjTtp: 4 tables
2025-07-10 15:45:22,014 - INFO - Retrieved schema for base apptyeo8OtplQ8wAN: 4 tables
2025-07-10 15:45:23,580 - INFO - Retrieved schema for base apptyeo8OtplQ8wAN: 4 tables
2025-07-10 15:57:03,238 - INFO - Found 8 accessible Airtable bases
2025-07-10 15:57:11,802 - INFO - Retrieved schema for base app7ffftdM6e3yekG: 8 tables
2025-07-10 15:57:13,811 - INFO - Retrieved schema for base appHschkYyi0nRLZR: 4 tables
2025-07-10 15:57:15,797 - INFO - Retrieved schema for base app7wu1MtoDXK0UBN: 4 tables
2025-07-10 15:57:17,863 - INFO - Retrieved schema for base apptyeo8OtplQ8wAN: 4 tables
2025-07-10 15:57:19,710 - INFO - Retrieved schema for base appJWDQxzECcXvWz7: 4 tables
2025-07-10 15:57:21,610 - INFO - Retrieved schema for base app5ldOYdhNAOaeOF: 4 tables
2025-07-10 15:57:23,555 - INFO - Retrieved schema for base app9JgRBZC2GNlaKM: 4 tables
2025-07-10 15:57:25,786 - INFO - Retrieved schema for base appL4rTljQgGkjTtp: 4 tables
2025-07-10 15:57:37,899 - INFO - Retrieved schema for base apptyeo8OtplQ8wAN: 4 tables
2025-07-10 15:57:48,487 - INFO - Retrieved schema for base apptyeo8OtplQ8wAN: 4 tables
2025-07-10 15:58:00,736 - INFO - Retrieved schema for base apptyeo8OtplQ8wAN: 4 tables
2025-07-10 16:04:18,725 - INFO - Application cleanup completed
2025-07-10 16:05:41,743 - INFO - Found 8 accessible Airtable bases
2025-07-10 16:05:42,556 - INFO - Retrieved schema for base app7ffftdM6e3yekG: 8 tables
2025-07-10 16:05:44,494 - INFO - Retrieved schema for base appHschkYyi0nRLZR: 4 tables
2025-07-10 16:05:46,523 - INFO - Retrieved schema for base app7wu1MtoDXK0UBN: 4 tables
2025-07-10 16:05:48,599 - INFO - Retrieved schema for base apptyeo8OtplQ8wAN: 4 tables
2025-07-10 16:05:50,452 - INFO - Retrieved schema for base appJWDQxzECcXvWz7: 4 tables
2025-07-10 16:05:52,208 - INFO - Retrieved schema for base app5ldOYdhNAOaeOF: 4 tables
2025-07-10 16:05:54,158 - INFO - Retrieved schema for base app9JgRBZC2GNlaKM: 4 tables
2025-07-10 16:05:56,075 - INFO - Retrieved schema for base appL4rTljQgGkjTtp: 4 tables
2025-07-10 16:06:07,803 - INFO - Retrieved schema for base apptyeo8OtplQ8wAN: 4 tables
2025-07-10 16:20:03,760 - INFO - Retrieved schema for base appJWDQxzECcXvWz7: 4 tables
2025-07-10 16:20:06,663 - INFO - Retrieved schema for base apptyeo8OtplQ8wAN: 4 tables
2025-07-10 16:20:07,870 - INFO - Retrieved schema for base apptyeo8OtplQ8wAN: 4 tables
2025-07-10 16:53:03,757 - INFO - Retrieved schema for base apptyeo8OtplQ8wAN: 4 tables
2025-07-10 16:53:14,482 - INFO - Retrieved schema for base apptyeo8OtplQ8wAN: 4 tables
2025-07-10 17:06:02,958 - INFO - Retrieved schema for base app7ffftdM6e3yekG: 8 tables
2025-07-10 17:06:05,173 - INFO - Retrieved schema for base app7ffftdM6e3yekG: 8 tables
2025-07-10 17:24:22,400 - INFO - Application cleanup completed
2025-07-10 17:24:23,222 - INFO - Application cleanup completed
2025-07-10 17:24:45,052 - INFO - Found 8 accessible Airtable bases
2025-07-10 17:24:45,841 - INFO - Retrieved schema for base app7ffftdM6e3yekG: 8 tables
2025-07-10 17:24:47,820 - INFO - Retrieved schema for base appHschkYyi0nRLZR: 4 tables
2025-07-10 17:24:49,799 - INFO - Retrieved schema for base app7wu1MtoDXK0UBN: 4 tables
2025-07-10 17:24:51,845 - INFO - Retrieved schema for base apptyeo8OtplQ8wAN: 4 tables
2025-07-10 17:24:53,715 - INFO - Retrieved schema for base appJWDQxzECcXvWz7: 4 tables
2025-07-10 17:24:55,547 - INFO - Retrieved schema for base app5ldOYdhNAOaeOF: 4 tables
2025-07-10 17:24:57,582 - INFO - Retrieved schema for base app9JgRBZC2GNlaKM: 4 tables
2025-07-10 17:24:59,517 - INFO - Retrieved schema for base appL4rTljQgGkjTtp: 4 tables
2025-07-10 17:25:24,990 - INFO - Retrieved schema for base apptyeo8OtplQ8wAN: 4 tables
2025-07-10 17:31:32,121 - DEBUG - Starting new HTTPS connection (1): api.airtable.com:443
2025-07-10 17:31:32,884 - DEBUG - https://api.airtable.com:443 "GET /v0/meta/bases HTTP/1.1" 200 278
2025-07-10 17:31:32,911 - DEBUG - Starting new HTTPS connection (1): api.airtable.com:443
2025-07-10 17:31:33,562 - DEBUG - https://api.airtable.com:443 "GET /v0/meta/bases HTTP/1.1" 200 278
2025-07-10 17:31:33,563 - INFO - Found 8 accessible Airtable bases
2025-07-10 17:31:33,573 - DEBUG - Starting new HTTPS connection (1): api.airtable.com:443
2025-07-10 17:31:34,337 - DEBUG - https://api.airtable.com:443 "GET /v0/meta/bases/app7ffftdM6e3yekG/tables HTTP/1.1" 200 25478
2025-07-10 17:31:34,340 - INFO - Retrieved schema for base app7ffftdM6e3yekG: 8 tables
2025-07-10 17:31:34,352 - DEBUG - Starting new HTTPS connection (1): api.airtable.com:443
2025-07-10 17:31:36,328 - DEBUG - https://api.airtable.com:443 "GET /v0/meta/bases/appHschkYyi0nRLZR/tables HTTP/1.1" 200 2729
2025-07-10 17:31:36,329 - INFO - Retrieved schema for base appHschkYyi0nRLZR: 4 tables
2025-07-10 17:31:36,363 - DEBUG - Starting new HTTPS connection (1): api.airtable.com:443
2025-07-10 17:31:38,342 - DEBUG - https://api.airtable.com:443 "GET /v0/meta/bases/app7wu1MtoDXK0UBN/tables HTTP/1.1" 200 2823
2025-07-10 17:31:38,343 - INFO - Retrieved schema for base app7wu1MtoDXK0UBN: 4 tables
2025-07-10 17:31:38,346 - DEBUG - Starting new HTTPS connection (1): api.airtable.com:443
2025-07-10 17:31:39,132 - DEBUG - https://api.airtable.com:443 "GET /v0/meta/bases/apptyeo8OtplQ8wAN/tables HTTP/1.1" 200 1142
2025-07-10 17:31:39,133 - INFO - Retrieved schema for base apptyeo8OtplQ8wAN: 1 tables
2025-07-10 17:31:39,136 - DEBUG - Starting new HTTPS connection (1): api.airtable.com:443
2025-07-10 17:31:40,914 - DEBUG - https://api.airtable.com:443 "GET /v0/meta/bases/appJWDQxzECcXvWz7/tables HTTP/1.1" 200 4325
2025-07-10 17:31:40,915 - INFO - Retrieved schema for base appJWDQxzECcXvWz7: 4 tables
2025-07-10 17:31:40,919 - DEBUG - Starting new HTTPS connection (1): api.airtable.com:443
2025-07-10 17:31:42,804 - DEBUG - https://api.airtable.com:443 "GET /v0/meta/bases/app5ldOYdhNAOaeOF/tables HTTP/1.1" 200 3270
2025-07-10 17:31:42,805 - INFO - Retrieved schema for base app5ldOYdhNAOaeOF: 4 tables
2025-07-10 17:31:42,815 - DEBUG - Starting new HTTPS connection (1): api.airtable.com:443
2025-07-10 17:31:44,861 - DEBUG - https://api.airtable.com:443 "GET /v0/meta/bases/app9JgRBZC2GNlaKM/tables HTTP/1.1" 200 4056
2025-07-10 17:31:44,862 - INFO - Retrieved schema for base app9JgRBZC2GNlaKM: 4 tables
2025-07-10 17:31:44,888 - DEBUG - Starting new HTTPS connection (1): api.airtable.com:443
2025-07-10 17:31:46,822 - DEBUG - https://api.airtable.com:443 "GET /v0/meta/bases/appL4rTljQgGkjTtp/tables HTTP/1.1" 200 3734
2025-07-10 17:31:46,823 - INFO - Retrieved schema for base appL4rTljQgGkjTtp: 4 tables
2025-07-10 17:31:46,824 - DEBUG - 🔍 Checking completeness for base: Quick Fix (app7ffftdM6e3yekG)
2025-07-10 17:31:46,825 - DEBUG -    📋 Existing tables: ['GHL', 'POS', 'Google Ads', 'Meta Ads', 'Meta Ads Summary', 'Meta Ads AT', 'Meta Ads Simplified', 'Meta Ads Performance']
2025-07-10 17:31:46,825 - DEBUG -    🔍 Looking for ghl table...
2025-07-10 17:31:46,825 - DEBUG -       ✅ Found exact match: GHL
2025-07-10 17:31:46,826 - DEBUG - 🔍 Checking fields for GHL (type: ghl)
2025-07-10 17:31:46,826 - DEBUG -    Required fields (10): ['Contact ID', 'First Name', 'Last Name', 'Email', 'Phone', 'Date Created', 'Lead Source', 'Status', 'Tags', 'Location']
2025-07-10 17:31:46,826 - DEBUG -    Existing fields (13): ['contact name', 'Location', 'phone', 'email', 'pipeline', 'stage', 'Lead Value', 'Date Created', 'Traffic Source', 'Channel', 'Conversion Event', 'Opportunity ID', 'Contact ID']
2025-07-10 17:31:46,826 - DEBUG -    ❌ Missing fields: ['First Name', 'Last Name', 'Email', 'Phone', 'Lead Source', 'Status', 'Tags']
2025-07-10 17:31:46,826 - DEBUG -       ⚠️ Table GHL missing 7 fields
2025-07-10 17:31:46,827 - DEBUG -    🔍 Looking for google_ads table...
2025-07-10 17:31:46,827 - DEBUG -       ✅ Found exact match: Google Ads
2025-07-10 17:31:46,827 - DEBUG - 🔍 Checking fields for Google Ads (type: google_ads)
2025-07-10 17:31:46,827 - DEBUG -    Required fields (12): ['Campaign ID', 'Campaign Name', 'Date', 'Impressions', 'Clicks', 'Cost', 'CTR', 'CPC', 'Conversions', 'Conversion Rate', 'Ad Group', 'Keywords']
2025-07-10 17:31:46,827 - DEBUG -    Existing fields (11): ['Date', 'Campaign ID', 'Campaign Name', 'Cost', 'Impressions', 'Clicks', 'Conversions', 'CTR', 'CPC', 'Conv. Rate', 'Cost per Conv.']
2025-07-10 17:31:46,827 - DEBUG -    ❌ Missing fields: ['Conversion Rate', 'Ad Group', 'Keywords']
2025-07-10 17:31:46,827 - DEBUG -       ⚠️ Table Google Ads missing 3 fields
2025-07-10 17:31:46,827 - DEBUG -    🔍 Looking for pos table...
2025-07-10 17:31:46,828 - DEBUG -       ✅ Found exact match: POS
2025-07-10 17:31:46,828 - DEBUG - 🔍 Checking fields for POS (type: pos)
2025-07-10 17:31:46,828 - DEBUG -    Required fields (10): ['Transaction ID', 'Customer Name', 'Customer Email', 'Customer Phone', 'Created', 'Total Amount', 'Payment Method', 'Items', 'Location', 'Status']
2025-07-10 17:31:46,828 - DEBUG -    Existing fields (9): ['Name', 'Company', 'Ticket Count', 'Ticket Amount', 'Location', 'Phone', 'Email', 'Created', 'Customer']
2025-07-10 17:31:46,828 - DEBUG -    ❌ Missing fields: ['Transaction ID', 'Customer Name', 'Customer Email', 'Customer Phone', 'Total Amount', 'Payment Method', 'Items', 'Status']
2025-07-10 17:31:46,828 - DEBUG -       ⚠️ Table POS missing 8 fields
2025-07-10 17:31:46,828 - DEBUG -    🔍 Looking for meta_ads table...
2025-07-10 17:31:46,828 - DEBUG -       ✅ Found exact match: Meta Ads
2025-07-10 17:31:46,828 - DEBUG - 🔍 Checking fields for Meta Ads (type: meta_ads)
2025-07-10 17:31:46,828 - DEBUG -    Required fields (12): ['Campaign ID', 'Campaign Name', 'Ad Set Name', 'Ad Name', 'Reporting starts', 'Reporting ends', 'Impressions', 'Clicks', 'Spend', 'CTR', 'CPC', 'Conversions']
2025-07-10 17:31:46,829 - DEBUG -    Existing fields (21): ['Reporting starts', 'Reporting ends', 'Ad name', 'Ad delivery', 'Ad Set Name', 'Bid', 'Bid type', 'Ad set budget', 'Ad set budget type', 'Last significant edit', 'Attribution setting', 'Results', 'Result indicator', 'Reach', 'Impressions', 'Cost per results', 'Quality ranking', 'Engagement rate ranking', 'Conversion rate ranking', 'Amount spent (USD)', 'Ends']
2025-07-10 17:31:46,829 - DEBUG -    ❌ Missing fields: ['Campaign ID', 'Campaign Name', 'Ad Name', 'Clicks', 'Spend', 'CTR', 'CPC', 'Conversions']
2025-07-10 17:31:46,829 - DEBUG -       ⚠️ Table Meta Ads missing 8 fields
2025-07-10 17:31:46,829 - DEBUG -    📊 Results: Missing tables: 0, Incomplete tables: 4
2025-07-10 17:31:46,829 - DEBUG -    🎯 Base complete: False
2025-07-10 17:31:46,830 - DEBUG - 🔍 Checking completeness for base: Fix My Gadget (appHschkYyi0nRLZR)
2025-07-10 17:31:46,830 - DEBUG -    📋 Existing tables: ['GHL', 'Google Ads', 'POS', 'Meta Ads']
2025-07-10 17:31:46,830 - DEBUG -    🔍 Looking for ghl table...
2025-07-10 17:31:46,830 - DEBUG -       ✅ Found exact match: GHL
2025-07-10 17:31:46,830 - DEBUG - 🔍 Checking fields for GHL (type: ghl)
2025-07-10 17:31:46,830 - DEBUG -    Required fields (10): ['Contact ID', 'First Name', 'Last Name', 'Email', 'Phone', 'Date Created', 'Lead Source', 'Status', 'Tags', 'Location']
2025-07-10 17:31:46,830 - DEBUG -    Existing fields (13): ['contact name', 'Location', 'phone', 'email', 'pipeline', 'stage', 'Lead Value', 'Date Created', 'Traffic Source', 'Channel', 'Conversion Event', 'Opportunity ID', 'Contact ID']
2025-07-10 17:31:46,830 - DEBUG -    ❌ Missing fields: ['First Name', 'Last Name', 'Email', 'Phone', 'Lead Source', 'Status', 'Tags']
2025-07-10 17:31:46,830 - DEBUG -       ⚠️ Table GHL missing 7 fields
2025-07-10 17:31:46,830 - DEBUG -    🔍 Looking for google_ads table...
2025-07-10 17:31:46,831 - DEBUG -       ✅ Found exact match: Google Ads
2025-07-10 17:31:46,831 - DEBUG - 🔍 Checking fields for Google Ads (type: google_ads)
2025-07-10 17:31:46,831 - DEBUG -    Required fields (12): ['Campaign ID', 'Campaign Name', 'Date', 'Impressions', 'Clicks', 'Cost', 'CTR', 'CPC', 'Conversions', 'Conversion Rate', 'Ad Group', 'Keywords']
2025-07-10 17:31:46,831 - DEBUG -    Existing fields (11): ['Campaign Name', 'Date', 'Impressions', 'Clicks', 'Cost', 'CTR', 'CPC', 'Conversions', 'Conversion Rate', 'Ad Group', 'Keywords']
2025-07-10 17:31:46,831 - DEBUG -    ❌ Missing fields: ['Campaign ID']
2025-07-10 17:31:46,831 - DEBUG -       ⚠️ Table Google Ads missing 1 fields
2025-07-10 17:31:46,831 - DEBUG -    🔍 Looking for pos table...
2025-07-10 17:31:46,831 - DEBUG -       ✅ Found exact match: POS
2025-07-10 17:31:46,831 - DEBUG - 🔍 Checking fields for POS (type: pos)
2025-07-10 17:31:46,831 - DEBUG -    Required fields (10): ['Transaction ID', 'Customer Name', 'Customer Email', 'Customer Phone', 'Created', 'Total Amount', 'Payment Method', 'Items', 'Location', 'Status']
2025-07-10 17:31:46,832 - DEBUG -    Existing fields (10): ['Transaction ID', 'Customer Name', 'Customer Email', 'Customer Phone', 'Created', 'Sale Amount', 'Payment Method', 'Service Type', 'Location', 'Notes']
2025-07-10 17:31:46,832 - DEBUG -    ❌ Missing fields: ['Total Amount', 'Items', 'Status']
2025-07-10 17:31:46,832 - DEBUG -       ⚠️ Table POS missing 3 fields
2025-07-10 17:31:46,832 - DEBUG -    🔍 Looking for meta_ads table...
2025-07-10 17:31:46,832 - DEBUG -       ✅ Found exact match: Meta Ads
2025-07-10 17:31:46,832 - DEBUG - 🔍 Checking fields for Meta Ads (type: meta_ads)
2025-07-10 17:31:46,833 - DEBUG -    Required fields (12): ['Campaign ID', 'Campaign Name', 'Ad Set Name', 'Ad Name', 'Reporting starts', 'Reporting ends', 'Impressions', 'Clicks', 'Spend', 'CTR', 'CPC', 'Conversions']
2025-07-10 17:31:46,833 - DEBUG -    Existing fields (14): ['Campaign Name', 'Ad Set Name', 'Ad Name', 'Reporting ends', 'Impressions', 'Clicks', 'Spend', 'CTR', 'CPC', 'CPM', 'Conversions', 'Conversion Rate', 'Platform', 'Objective']
2025-07-10 17:31:46,833 - DEBUG -    ❌ Missing fields: ['Campaign ID', 'Reporting starts']
2025-07-10 17:31:46,833 - DEBUG -       ⚠️ Table Meta Ads missing 2 fields
2025-07-10 17:31:46,833 - DEBUG -    📊 Results: Missing tables: 0, Incomplete tables: 4
2025-07-10 17:31:46,833 - DEBUG -    🎯 Base complete: False
2025-07-10 17:31:46,833 - DEBUG - 🔍 Checking completeness for base: Owl Repairs (app7wu1MtoDXK0UBN)
2025-07-10 17:31:46,833 - DEBUG -    📋 Existing tables: ['GHL', 'Google Ads Data', 'POS Sales Data', 'Meta Ads Data']
2025-07-10 17:31:46,833 - DEBUG -    🔍 Looking for ghl table...
2025-07-10 17:31:46,833 - DEBUG -       ✅ Found exact match: GHL
2025-07-10 17:31:46,834 - DEBUG - 🔍 Checking fields for GHL (type: ghl)
2025-07-10 17:31:46,834 - DEBUG -    Required fields (10): ['Contact ID', 'First Name', 'Last Name', 'Email', 'Phone', 'Date Created', 'Lead Source', 'Status', 'Tags', 'Location']
2025-07-10 17:31:46,834 - DEBUG -    Existing fields (15): ['contact name', 'Location', 'phone', 'email', 'pipeline', 'stage', 'Lead Value', 'Date Created', 'Traffic Source', 'Channel', 'Conversion Event', 'Opportunity ID', 'Contact ID', 'Field 13', 'Field 14']
2025-07-10 17:31:46,834 - DEBUG -    ❌ Missing fields: ['First Name', 'Last Name', 'Email', 'Phone', 'Lead Source', 'Status', 'Tags']
2025-07-10 17:31:46,834 - DEBUG -       ⚠️ Table GHL missing 7 fields
2025-07-10 17:31:46,834 - DEBUG -    🔍 Looking for google_ads table...
2025-07-10 17:31:46,834 - DEBUG -       ✅ Found partial match: Google Ads Data (contains 'google ads')
2025-07-10 17:31:46,834 - DEBUG - 🔍 Checking fields for Google Ads Data (type: google_ads)
2025-07-10 17:31:46,834 - DEBUG -    Required fields (12): ['Campaign ID', 'Campaign Name', 'Date', 'Impressions', 'Clicks', 'Cost', 'CTR', 'CPC', 'Conversions', 'Conversion Rate', 'Ad Group', 'Keywords']
2025-07-10 17:31:46,834 - DEBUG -    Existing fields (11): ['Campaign Name', 'Date', 'Impressions', 'Clicks', 'Cost', 'CTR', 'CPC', 'Conversions', 'Conversion Rate', 'Ad Group', 'Keywords']
2025-07-10 17:31:46,834 - DEBUG -    ❌ Missing fields: ['Campaign ID']
2025-07-10 17:31:46,835 - DEBUG -       ⚠️ Table Google Ads Data missing 1 fields
2025-07-10 17:31:46,835 - DEBUG -    🔍 Looking for pos table...
2025-07-10 17:31:46,835 - DEBUG -       ✅ Found partial match: POS Sales Data (contains 'pos')
2025-07-10 17:31:46,835 - DEBUG - 🔍 Checking fields for POS Sales Data (type: pos)
2025-07-10 17:31:46,835 - DEBUG -    Required fields (10): ['Transaction ID', 'Customer Name', 'Customer Email', 'Customer Phone', 'Created', 'Total Amount', 'Payment Method', 'Items', 'Location', 'Status']
2025-07-10 17:31:46,835 - DEBUG -    Existing fields (10): ['Transaction ID', 'Customer Name', 'Customer Email', 'Customer Phone', 'Created', 'Sale Amount', 'Payment Method', 'Service Type', 'Location', 'Notes']
2025-07-10 17:31:46,835 - DEBUG -    ❌ Missing fields: ['Total Amount', 'Items', 'Status']
2025-07-10 17:31:46,835 - DEBUG -       ⚠️ Table POS Sales Data missing 3 fields
2025-07-10 17:31:46,835 - DEBUG -    🔍 Looking for meta_ads table...
2025-07-10 17:31:46,836 - DEBUG -       ✅ Found partial match: Meta Ads Data (contains 'meta ads')
2025-07-10 17:31:46,836 - DEBUG - 🔍 Checking fields for Meta Ads Data (type: meta_ads)
2025-07-10 17:31:46,836 - DEBUG -    Required fields (12): ['Campaign ID', 'Campaign Name', 'Ad Set Name', 'Ad Name', 'Reporting starts', 'Reporting ends', 'Impressions', 'Clicks', 'Spend', 'CTR', 'CPC', 'Conversions']
2025-07-10 17:31:46,836 - DEBUG -    Existing fields (14): ['Campaign Name', 'Ad Set Name', 'Ad Name', 'Reporting ends', 'Impressions', 'Clicks', 'Spend', 'CTR', 'CPC', 'CPM', 'Conversions', 'Conversion Rate', 'Platform', 'Objective']
2025-07-10 17:31:46,836 - DEBUG -    ❌ Missing fields: ['Campaign ID', 'Reporting starts']
2025-07-10 17:31:46,836 - DEBUG -       ⚠️ Table Meta Ads Data missing 2 fields
2025-07-10 17:31:46,836 - DEBUG -    📊 Results: Missing tables: 0, Incomplete tables: 4
2025-07-10 17:31:46,836 - DEBUG -    🎯 Base complete: False
2025-07-10 17:31:46,836 - DEBUG - 🔍 Checking completeness for base: iGenius Repair (apptyeo8OtplQ8wAN)
2025-07-10 17:31:46,837 - DEBUG -    📋 Existing tables: ['GHL']
2025-07-10 17:31:46,837 - DEBUG -    🔍 Looking for ghl table...
2025-07-10 17:31:46,837 - DEBUG -       ✅ Found exact match: GHL
2025-07-10 17:31:46,837 - DEBUG - 🔍 Checking fields for GHL (type: ghl)
2025-07-10 17:31:46,837 - DEBUG -    Required fields (10): ['Contact ID', 'First Name', 'Last Name', 'Email', 'Phone', 'Date Created', 'Lead Source', 'Status', 'Tags', 'Location']
2025-07-10 17:31:46,837 - DEBUG -    Existing fields (13): ['contact name', 'Location', 'phone', 'email', 'pipeline', 'stage', 'Lead Value', 'Date Created', 'Traffic Source', 'Channel', 'Conversion Event', 'Opportunity ID', 'Contact ID']
2025-07-10 17:31:46,837 - DEBUG -    ❌ Missing fields: ['First Name', 'Last Name', 'Email', 'Phone', 'Lead Source', 'Status', 'Tags']
2025-07-10 17:31:46,837 - DEBUG -       ⚠️ Table GHL missing 7 fields
2025-07-10 17:31:46,837 - DEBUG -    🔍 Looking for google_ads table...
2025-07-10 17:31:46,838 - DEBUG -       ❌ No google_ads table found
2025-07-10 17:31:46,838 - DEBUG -    🔍 Looking for pos table...
2025-07-10 17:31:46,838 - DEBUG -       ❌ No pos table found
2025-07-10 17:31:46,838 - DEBUG -    🔍 Looking for meta_ads table...
2025-07-10 17:31:46,838 - DEBUG -       ❌ No meta_ads table found
2025-07-10 17:31:46,838 - DEBUG -    📊 Results: Missing tables: 3, Incomplete tables: 1
2025-07-10 17:31:46,838 - DEBUG -    🎯 Base complete: False
2025-07-10 17:31:46,838 - DEBUG - 🔍 Checking completeness for base: Ready Set Repair (appJWDQxzECcXvWz7)
2025-07-10 17:31:46,838 - DEBUG -    📋 Existing tables: ['GHL', 'Google Ads', 'POS', 'Meta Ads']
2025-07-10 17:31:46,839 - DEBUG -    🔍 Looking for ghl table...
2025-07-10 17:31:46,839 - DEBUG -       ✅ Found exact match: GHL
2025-07-10 17:31:46,839 - DEBUG - 🔍 Checking fields for GHL (type: ghl)
2025-07-10 17:31:46,839 - DEBUG -    Required fields (10): ['Contact ID', 'First Name', 'Last Name', 'Email', 'Phone', 'Date Created', 'Lead Source', 'Status', 'Tags', 'Location']
2025-07-10 17:31:46,839 - DEBUG -    Existing fields (8): ['Contact Name', 'Email', 'Phone', 'Date Created', 'Lead Source', 'Status', 'Tags', 'Notes']
2025-07-10 17:31:46,839 - DEBUG -    ❌ Missing fields: ['Contact ID', 'First Name', 'Last Name', 'Location']
2025-07-10 17:31:46,839 - DEBUG -       ⚠️ Table GHL missing 4 fields
2025-07-10 17:31:46,839 - DEBUG -    🔍 Looking for google_ads table...
2025-07-10 17:31:46,840 - DEBUG -       ✅ Found exact match: Google Ads
2025-07-10 17:31:46,840 - DEBUG - 🔍 Checking fields for Google Ads (type: google_ads)
2025-07-10 17:31:46,840 - DEBUG -    Required fields (12): ['Campaign ID', 'Campaign Name', 'Date', 'Impressions', 'Clicks', 'Cost', 'CTR', 'CPC', 'Conversions', 'Conversion Rate', 'Ad Group', 'Keywords']
2025-07-10 17:31:46,840 - DEBUG -    Existing fields (11): ['Campaign Name', 'Date', 'Impressions', 'Clicks', 'Cost', 'CTR', 'CPC', 'Conversions', 'Conversion Rate', 'Ad Group', 'Keywords']
2025-07-10 17:31:46,840 - DEBUG -    ❌ Missing fields: ['Campaign ID']
2025-07-10 17:31:46,840 - DEBUG -       ⚠️ Table Google Ads missing 1 fields
2025-07-10 17:31:46,840 - DEBUG -    🔍 Looking for pos table...
2025-07-10 17:31:46,840 - DEBUG -       ✅ Found exact match: POS
2025-07-10 17:31:46,840 - DEBUG - 🔍 Checking fields for POS (type: pos)
2025-07-10 17:31:46,840 - DEBUG -    Required fields (10): ['Transaction ID', 'Customer Name', 'Customer Email', 'Customer Phone', 'Created', 'Total Amount', 'Payment Method', 'Items', 'Location', 'Status']
2025-07-10 17:31:46,840 - DEBUG -    Existing fields (10): ['Transaction ID', 'Customer Name', 'Customer Email', 'Customer Phone', 'Created', 'Sale Amount', 'Payment Method', 'Service Type', 'Location', 'Notes']
2025-07-10 17:31:46,841 - DEBUG -    ❌ Missing fields: ['Total Amount', 'Items', 'Status']
2025-07-10 17:31:46,841 - DEBUG -       ⚠️ Table POS missing 3 fields
2025-07-10 17:31:46,841 - DEBUG -    🔍 Looking for meta_ads table...
2025-07-10 17:31:46,841 - DEBUG -       ✅ Found exact match: Meta Ads
2025-07-10 17:31:46,841 - DEBUG - 🔍 Checking fields for Meta Ads (type: meta_ads)
2025-07-10 17:31:46,841 - DEBUG -    Required fields (12): ['Campaign ID', 'Campaign Name', 'Ad Set Name', 'Ad Name', 'Reporting starts', 'Reporting ends', 'Impressions', 'Clicks', 'Spend', 'CTR', 'CPC', 'Conversions']
2025-07-10 17:31:46,841 - DEBUG -    Existing fields (14): ['Campaign Name', 'Ad Set Name', 'Ad Name', 'Reporting ends', 'Impressions', 'Clicks', 'Spend', 'CTR', 'CPC', 'CPM', 'Conversions', 'Conversion Rate', 'Platform', 'Objective']
2025-07-10 17:31:46,841 - DEBUG -    ❌ Missing fields: ['Campaign ID', 'Reporting starts']
2025-07-10 17:31:46,842 - DEBUG -       ⚠️ Table Meta Ads missing 2 fields
2025-07-10 17:31:46,842 - DEBUG -    📊 Results: Missing tables: 0, Incomplete tables: 4
2025-07-10 17:31:46,842 - DEBUG -    🎯 Base complete: False
2025-07-10 17:31:46,842 - DEBUG - 🔍 Checking completeness for base: MobileComm (app5ldOYdhNAOaeOF)
2025-07-10 17:31:46,842 - DEBUG -    📋 Existing tables: ['GHL', 'Google Ads', 'POS', 'Meta Ads']
2025-07-10 17:31:46,842 - DEBUG -    🔍 Looking for ghl table...
2025-07-10 17:31:46,842 - DEBUG -       ✅ Found exact match: GHL
2025-07-10 17:31:46,842 - DEBUG - 🔍 Checking fields for GHL (type: ghl)
2025-07-10 17:31:46,843 - DEBUG -    Required fields (10): ['Contact ID', 'First Name', 'Last Name', 'Email', 'Phone', 'Date Created', 'Lead Source', 'Status', 'Tags', 'Location']
2025-07-10 17:31:46,843 - DEBUG -    Existing fields (8): ['Contact Name', 'Email', 'Phone', 'Date Created', 'Lead Source', 'Status', 'Tags', 'Notes']
2025-07-10 17:31:46,843 - DEBUG -    ❌ Missing fields: ['Contact ID', 'First Name', 'Last Name', 'Location']
2025-07-10 17:31:46,843 - DEBUG -       ⚠️ Table GHL missing 4 fields
2025-07-10 17:31:46,843 - DEBUG -    🔍 Looking for google_ads table...
2025-07-10 17:31:46,843 - DEBUG -       ✅ Found exact match: Google Ads
2025-07-10 17:31:46,843 - DEBUG - 🔍 Checking fields for Google Ads (type: google_ads)
2025-07-10 17:31:46,843 - DEBUG -    Required fields (12): ['Campaign ID', 'Campaign Name', 'Date', 'Impressions', 'Clicks', 'Cost', 'CTR', 'CPC', 'Conversions', 'Conversion Rate', 'Ad Group', 'Keywords']
2025-07-10 17:31:46,843 - DEBUG -    Existing fields (11): ['Campaign Name', 'Date', 'Impressions', 'Clicks', 'Cost', 'CTR', 'CPC', 'Conversions', 'Conversion Rate', 'Ad Group', 'Keywords']
2025-07-10 17:31:46,843 - DEBUG -    ❌ Missing fields: ['Campaign ID']
2025-07-10 17:31:46,844 - DEBUG -       ⚠️ Table Google Ads missing 1 fields
2025-07-10 17:31:46,844 - DEBUG -    🔍 Looking for pos table...
2025-07-10 17:31:46,844 - DEBUG -       ✅ Found exact match: POS
2025-07-10 17:31:46,844 - DEBUG - 🔍 Checking fields for POS (type: pos)
2025-07-10 17:31:46,844 - DEBUG -    Required fields (10): ['Transaction ID', 'Customer Name', 'Customer Email', 'Customer Phone', 'Created', 'Total Amount', 'Payment Method', 'Items', 'Location', 'Status']
2025-07-10 17:31:46,844 - DEBUG -    Existing fields (10): ['Transaction ID', 'Customer Name', 'Customer Email', 'Customer Phone', 'Created', 'Sale Amount', 'Payment Method', 'Service Type', 'Location', 'Notes']
2025-07-10 17:31:46,844 - DEBUG -    ❌ Missing fields: ['Total Amount', 'Items', 'Status']
2025-07-10 17:31:46,844 - DEBUG -       ⚠️ Table POS missing 3 fields
2025-07-10 17:31:46,844 - DEBUG -    🔍 Looking for meta_ads table...
2025-07-10 17:31:46,844 - DEBUG -       ✅ Found exact match: Meta Ads
2025-07-10 17:31:46,844 - DEBUG - 🔍 Checking fields for Meta Ads (type: meta_ads)
2025-07-10 17:31:46,845 - DEBUG -    Required fields (12): ['Campaign ID', 'Campaign Name', 'Ad Set Name', 'Ad Name', 'Reporting starts', 'Reporting ends', 'Impressions', 'Clicks', 'Spend', 'CTR', 'CPC', 'Conversions']
2025-07-10 17:31:46,845 - DEBUG -    Existing fields (14): ['Campaign Name', 'Ad Set Name', 'Ad Name', 'Reporting ends', 'Impressions', 'Clicks', 'Spend', 'CTR', 'CPC', 'CPM', 'Conversions', 'Conversion Rate', 'Platform', 'Objective']
2025-07-10 17:31:46,845 - DEBUG -    ❌ Missing fields: ['Campaign ID', 'Reporting starts']
2025-07-10 17:31:46,845 - DEBUG -       ⚠️ Table Meta Ads missing 2 fields
2025-07-10 17:31:46,845 - DEBUG -    📊 Results: Missing tables: 0, Incomplete tables: 4
2025-07-10 17:31:46,845 - DEBUG -    🎯 Base complete: False
2025-07-10 17:31:46,845 - DEBUG - 🔍 Checking completeness for base: Cellular Zone (app9JgRBZC2GNlaKM)
2025-07-10 17:31:46,845 - DEBUG -    📋 Existing tables: ['GHL', 'Google Ads', 'POS', 'Meta Ads']
2025-07-10 17:31:46,845 - DEBUG -    🔍 Looking for ghl table...
2025-07-10 17:31:46,845 - DEBUG -       ✅ Found exact match: GHL
2025-07-10 17:31:46,846 - DEBUG - 🔍 Checking fields for GHL (type: ghl)
2025-07-10 17:31:46,846 - DEBUG -    Required fields (10): ['Contact ID', 'First Name', 'Last Name', 'Email', 'Phone', 'Date Created', 'Lead Source', 'Status', 'Tags', 'Location']
2025-07-10 17:31:46,846 - DEBUG -    Existing fields (8): ['Contact Name', 'Email', 'Phone', 'Date Created', 'Lead Source', 'Status', 'Tags', 'Notes']
2025-07-10 17:31:46,846 - DEBUG -    ❌ Missing fields: ['Contact ID', 'First Name', 'Last Name', 'Location']
2025-07-10 17:31:46,846 - DEBUG -       ⚠️ Table GHL missing 4 fields
2025-07-10 17:31:46,846 - DEBUG -    🔍 Looking for google_ads table...
2025-07-10 17:31:46,846 - DEBUG -       ✅ Found exact match: Google Ads
2025-07-10 17:31:46,846 - DEBUG - 🔍 Checking fields for Google Ads (type: google_ads)
2025-07-10 17:31:46,846 - DEBUG -    Required fields (12): ['Campaign ID', 'Campaign Name', 'Date', 'Impressions', 'Clicks', 'Cost', 'CTR', 'CPC', 'Conversions', 'Conversion Rate', 'Ad Group', 'Keywords']
2025-07-10 17:31:46,846 - DEBUG -    Existing fields (11): ['Campaign Name', 'Date', 'Impressions', 'Clicks', 'Cost', 'CTR', 'CPC', 'Conversions', 'Conversion Rate', 'Ad Group', 'Keywords']
2025-07-10 17:31:46,846 - DEBUG -    ❌ Missing fields: ['Campaign ID']
2025-07-10 17:31:46,847 - DEBUG -       ⚠️ Table Google Ads missing 1 fields
2025-07-10 17:31:46,847 - DEBUG -    🔍 Looking for pos table...
2025-07-10 17:31:46,847 - DEBUG -       ✅ Found exact match: POS
2025-07-10 17:31:46,847 - DEBUG - 🔍 Checking fields for POS (type: pos)
2025-07-10 17:31:46,847 - DEBUG -    Required fields (10): ['Transaction ID', 'Customer Name', 'Customer Email', 'Customer Phone', 'Created', 'Total Amount', 'Payment Method', 'Items', 'Location', 'Status']
2025-07-10 17:31:46,847 - DEBUG -    Existing fields (10): ['Transaction ID', 'Customer Name', 'Customer Email', 'Customer Phone', 'Created', 'Sale Amount', 'Payment Method', 'Service Type', 'Location', 'Notes']
2025-07-10 17:31:46,847 - DEBUG -    ❌ Missing fields: ['Total Amount', 'Items', 'Status']
2025-07-10 17:31:46,847 - DEBUG -       ⚠️ Table POS missing 3 fields
2025-07-10 17:31:46,847 - DEBUG -    🔍 Looking for meta_ads table...
2025-07-10 17:31:46,848 - DEBUG -       ✅ Found exact match: Meta Ads
2025-07-10 17:31:46,848 - DEBUG - 🔍 Checking fields for Meta Ads (type: meta_ads)
2025-07-10 17:31:46,848 - DEBUG -    Required fields (12): ['Campaign ID', 'Campaign Name', 'Ad Set Name', 'Ad Name', 'Reporting starts', 'Reporting ends', 'Impressions', 'Clicks', 'Spend', 'CTR', 'CPC', 'Conversions']
2025-07-10 17:31:46,848 - DEBUG -    Existing fields (14): ['Campaign Name', 'Ad Set Name', 'Ad Name', 'Reporting ends', 'Impressions', 'Clicks', 'Spend', 'CTR', 'CPC', 'CPM', 'Conversions', 'Conversion Rate', 'Platform', 'Objective']
2025-07-10 17:31:46,848 - DEBUG -    ❌ Missing fields: ['Campaign ID', 'Reporting starts']
2025-07-10 17:31:46,848 - DEBUG -       ⚠️ Table Meta Ads missing 2 fields
2025-07-10 17:31:46,848 - DEBUG -    📊 Results: Missing tables: 0, Incomplete tables: 4
2025-07-10 17:31:46,848 - DEBUG -    🎯 Base complete: False
2025-07-10 17:31:46,848 - DEBUG - 🔍 Checking completeness for base: Gadget Repair LV (appL4rTljQgGkjTtp)
2025-07-10 17:31:46,849 - DEBUG -    📋 Existing tables: ['GHL', 'Google Ads', 'POS', 'Meta Ads']
2025-07-10 17:31:46,849 - DEBUG -    🔍 Looking for ghl table...
2025-07-10 17:31:46,849 - DEBUG -       ✅ Found exact match: GHL
2025-07-10 17:31:46,849 - DEBUG - 🔍 Checking fields for GHL (type: ghl)
2025-07-10 17:31:46,849 - DEBUG -    Required fields (10): ['Contact ID', 'First Name', 'Last Name', 'Email', 'Phone', 'Date Created', 'Lead Source', 'Status', 'Tags', 'Location']
2025-07-10 17:31:46,849 - DEBUG -    Existing fields (8): ['Contact Name', 'Email', 'Phone', 'Date Created', 'Lead Source', 'Status', 'Tags', 'Notes']
2025-07-10 17:31:46,849 - DEBUG -    ❌ Missing fields: ['Contact ID', 'First Name', 'Last Name', 'Location']
2025-07-10 17:31:46,849 - DEBUG -       ⚠️ Table GHL missing 4 fields
2025-07-10 17:31:46,849 - DEBUG -    🔍 Looking for google_ads table...
2025-07-10 17:31:46,850 - DEBUG -       ✅ Found exact match: Google Ads
2025-07-10 17:31:46,850 - DEBUG - 🔍 Checking fields for Google Ads (type: google_ads)
2025-07-10 17:31:46,850 - DEBUG -    Required fields (12): ['Campaign ID', 'Campaign Name', 'Date', 'Impressions', 'Clicks', 'Cost', 'CTR', 'CPC', 'Conversions', 'Conversion Rate', 'Ad Group', 'Keywords']
2025-07-10 17:31:46,850 - DEBUG -    Existing fields (11): ['Campaign Name', 'Date', 'Impressions', 'Clicks', 'Cost', 'CTR', 'CPC', 'Conversions', 'Conversion Rate', 'Ad Group', 'Keywords']
2025-07-10 17:31:46,850 - DEBUG -    ❌ Missing fields: ['Campaign ID']
2025-07-10 17:31:46,850 - DEBUG -       ⚠️ Table Google Ads missing 1 fields
2025-07-10 17:31:46,850 - DEBUG -    🔍 Looking for pos table...
2025-07-10 17:31:46,850 - DEBUG -       ✅ Found exact match: POS
2025-07-10 17:31:46,850 - DEBUG - 🔍 Checking fields for POS (type: pos)
2025-07-10 17:31:46,850 - DEBUG -    Required fields (10): ['Transaction ID', 'Customer Name', 'Customer Email', 'Customer Phone', 'Created', 'Total Amount', 'Payment Method', 'Items', 'Location', 'Status']
2025-07-10 17:31:46,850 - DEBUG -    Existing fields (10): ['Transaction ID', 'Customer Name', 'Customer Email', 'Customer Phone', 'Created', 'Sale Amount', 'Payment Method', 'Service Type', 'Location', 'Notes']
2025-07-10 17:31:46,851 - DEBUG -    ❌ Missing fields: ['Total Amount', 'Items', 'Status']
2025-07-10 17:31:46,851 - DEBUG -       ⚠️ Table POS missing 3 fields
2025-07-10 17:31:46,851 - DEBUG -    🔍 Looking for meta_ads table...
2025-07-10 17:31:46,851 - DEBUG -       ✅ Found exact match: Meta Ads
2025-07-10 17:31:46,851 - DEBUG - 🔍 Checking fields for Meta Ads (type: meta_ads)
2025-07-10 17:31:46,851 - DEBUG -    Required fields (12): ['Campaign ID', 'Campaign Name', 'Ad Set Name', 'Ad Name', 'Reporting starts', 'Reporting ends', 'Impressions', 'Clicks', 'Spend', 'CTR', 'CPC', 'Conversions']
2025-07-10 17:31:46,851 - DEBUG -    Existing fields (14): ['Campaign Name', 'Ad Set Name', 'Ad Name', 'Reporting ends', 'Impressions', 'Clicks', 'Spend', 'CTR', 'CPC', 'CPM', 'Conversions', 'Conversion Rate', 'Platform', 'Objective']
2025-07-10 17:31:46,851 - DEBUG -    ❌ Missing fields: ['Campaign ID', 'Reporting starts']
2025-07-10 17:31:46,851 - DEBUG -       ⚠️ Table Meta Ads missing 2 fields
2025-07-10 17:31:46,851 - DEBUG -    📊 Results: Missing tables: 0, Incomplete tables: 4
2025-07-10 17:31:46,852 - DEBUG -    🎯 Base complete: False
2025-07-10 17:31:46,892 - DEBUG - Starting new HTTPS connection (1): api.airtable.com:443
2025-07-10 17:31:47,471 - DEBUG - https://api.airtable.com:443 "GET /v0/meta/bases/app5ldOYdhNAOaeOF/tables HTTP/1.1" 200 3270
2025-07-10 17:31:47,471 - INFO - Retrieved schema for base app5ldOYdhNAOaeOF: 4 tables
2025-07-10 17:31:47,547 - DEBUG - 🔍 Checking completeness for base: ⚠️ MobileComm (app5ldOYdhNAOaeOF)
2025-07-10 17:31:47,547 - DEBUG -    📋 Existing tables: ['GHL', 'Google Ads', 'POS', 'Meta Ads']
2025-07-10 17:31:47,548 - DEBUG -    🔍 Looking for ghl table...
2025-07-10 17:31:47,548 - DEBUG -       ✅ Found exact match: GHL
2025-07-10 17:31:47,548 - DEBUG - 🔍 Checking fields for GHL (type: ghl)
2025-07-10 17:31:47,548 - DEBUG -    Required fields (10): ['Contact ID', 'First Name', 'Last Name', 'Email', 'Phone', 'Date Created', 'Lead Source', 'Status', 'Tags', 'Location']
2025-07-10 17:31:47,549 - DEBUG -    Existing fields (8): ['Contact Name', 'Email', 'Phone', 'Date Created', 'Lead Source', 'Status', 'Tags', 'Notes']
2025-07-10 17:31:47,549 - DEBUG -    ❌ Missing fields: ['Contact ID', 'First Name', 'Last Name', 'Location']
2025-07-10 17:31:47,549 - DEBUG -       ⚠️ Table GHL missing 4 fields
2025-07-10 17:31:47,549 - DEBUG -    🔍 Looking for google_ads table...
2025-07-10 17:31:47,549 - DEBUG -       ✅ Found exact match: Google Ads
2025-07-10 17:31:47,549 - DEBUG - 🔍 Checking fields for Google Ads (type: google_ads)
2025-07-10 17:31:47,549 - DEBUG -    Required fields (12): ['Campaign ID', 'Campaign Name', 'Date', 'Impressions', 'Clicks', 'Cost', 'CTR', 'CPC', 'Conversions', 'Conversion Rate', 'Ad Group', 'Keywords']
2025-07-10 17:31:47,550 - DEBUG -    Existing fields (11): ['Campaign Name', 'Date', 'Impressions', 'Clicks', 'Cost', 'CTR', 'CPC', 'Conversions', 'Conversion Rate', 'Ad Group', 'Keywords']
2025-07-10 17:31:47,550 - DEBUG -    ❌ Missing fields: ['Campaign ID']
2025-07-10 17:31:47,550 - DEBUG -       ⚠️ Table Google Ads missing 1 fields
2025-07-10 17:31:47,550 - DEBUG -    🔍 Looking for pos table...
2025-07-10 17:31:47,550 - DEBUG -       ✅ Found exact match: POS
2025-07-10 17:31:47,550 - DEBUG - 🔍 Checking fields for POS (type: pos)
2025-07-10 17:31:47,550 - DEBUG -    Required fields (10): ['Transaction ID', 'Customer Name', 'Customer Email', 'Customer Phone', 'Created', 'Total Amount', 'Payment Method', 'Items', 'Location', 'Status']
2025-07-10 17:31:47,550 - DEBUG -    Existing fields (10): ['Transaction ID', 'Customer Name', 'Customer Email', 'Customer Phone', 'Created', 'Sale Amount', 'Payment Method', 'Service Type', 'Location', 'Notes']
2025-07-10 17:31:47,550 - DEBUG -    ❌ Missing fields: ['Total Amount', 'Items', 'Status']
2025-07-10 17:31:47,550 - DEBUG -       ⚠️ Table POS missing 3 fields
2025-07-10 17:31:47,550 - DEBUG -    🔍 Looking for meta_ads table...
2025-07-10 17:31:47,551 - DEBUG -       ✅ Found exact match: Meta Ads
2025-07-10 17:31:47,551 - DEBUG - 🔍 Checking fields for Meta Ads (type: meta_ads)
2025-07-10 17:31:47,551 - DEBUG -    Required fields (12): ['Campaign ID', 'Campaign Name', 'Ad Set Name', 'Ad Name', 'Reporting starts', 'Reporting ends', 'Impressions', 'Clicks', 'Spend', 'CTR', 'CPC', 'Conversions']
2025-07-10 17:31:47,551 - DEBUG -    Existing fields (14): ['Campaign Name', 'Ad Set Name', 'Ad Name', 'Reporting ends', 'Impressions', 'Clicks', 'Spend', 'CTR', 'CPC', 'CPM', 'Conversions', 'Conversion Rate', 'Platform', 'Objective']
2025-07-10 17:31:47,551 - DEBUG -    ❌ Missing fields: ['Campaign ID', 'Reporting starts']
2025-07-10 17:31:47,551 - DEBUG -       ⚠️ Table Meta Ads missing 2 fields
2025-07-10 17:31:47,551 - DEBUG -    📊 Results: Missing tables: 0, Incomplete tables: 4
2025-07-10 17:31:47,551 - DEBUG -    🎯 Base complete: False
2025-07-10 17:31:54,932 - DEBUG - Starting new HTTPS connection (1): api.airtable.com:443
2025-07-10 17:31:55,619 - DEBUG - https://api.airtable.com:443 "GET /v0/meta/bases/apptyeo8OtplQ8wAN/tables HTTP/1.1" 200 1142
2025-07-10 17:31:55,620 - INFO - Retrieved schema for base apptyeo8OtplQ8wAN: 1 tables
2025-07-10 17:31:55,653 - DEBUG - 🔍 Checking completeness for base: ⚠️ iGenius Repair (apptyeo8OtplQ8wAN)
2025-07-10 17:31:55,654 - DEBUG -    📋 Existing tables: ['GHL']
2025-07-10 17:31:55,654 - DEBUG -    🔍 Looking for ghl table...
2025-07-10 17:31:55,654 - DEBUG -       ✅ Found exact match: GHL
2025-07-10 17:31:55,654 - DEBUG - 🔍 Checking fields for GHL (type: ghl)
2025-07-10 17:31:55,655 - DEBUG -    Required fields (10): ['Contact ID', 'First Name', 'Last Name', 'Email', 'Phone', 'Date Created', 'Lead Source', 'Status', 'Tags', 'Location']
2025-07-10 17:31:55,655 - DEBUG -    Existing fields (13): ['contact name', 'Location', 'phone', 'email', 'pipeline', 'stage', 'Lead Value', 'Date Created', 'Traffic Source', 'Channel', 'Conversion Event', 'Opportunity ID', 'Contact ID']
2025-07-10 17:31:55,655 - DEBUG -    ❌ Missing fields: ['First Name', 'Last Name', 'Email', 'Phone', 'Lead Source', 'Status', 'Tags']
2025-07-10 17:31:55,655 - DEBUG -       ⚠️ Table GHL missing 7 fields
2025-07-10 17:31:55,655 - DEBUG -    🔍 Looking for google_ads table...
2025-07-10 17:31:55,655 - DEBUG -       ❌ No google_ads table found
2025-07-10 17:31:55,655 - DEBUG -    🔍 Looking for pos table...
2025-07-10 17:31:55,656 - DEBUG -       ❌ No pos table found
2025-07-10 17:31:55,656 - DEBUG -    🔍 Looking for meta_ads table...
2025-07-10 17:31:55,656 - DEBUG -       ❌ No meta_ads table found
2025-07-10 17:31:55,656 - DEBUG -    📊 Results: Missing tables: 3, Incomplete tables: 1
2025-07-10 17:31:55,656 - DEBUG -    🎯 Base complete: False
2025-07-10 17:32:08,991 - INFO - Creating table 'Google Ads' in base apptyeo8OtplQ8wAN
2025-07-10 17:32:08,991 - INFO - Template has 12 fields
2025-07-10 17:32:08,994 - DEBUG - Starting new HTTPS connection (1): api.airtable.com:443
2025-07-10 17:32:09,789 - DEBUG - https://api.airtable.com:443 "POST /v0/meta/bases/apptyeo8OtplQ8wAN/tables HTTP/1.1" 200 535
2025-07-10 17:32:09,789 - INFO - Created table: Google Ads in base apptyeo8OtplQ8wAN
2025-07-10 17:32:09,790 - INFO - Successfully created table 'Google Ads'
2025-07-10 17:32:09,802 - INFO - Creating table 'POS' in base apptyeo8OtplQ8wAN
2025-07-10 17:32:09,802 - INFO - Template has 10 fields
2025-07-10 17:32:09,803 - DEBUG - Starting new HTTPS connection (1): api.airtable.com:443
2025-07-10 17:32:10,519 - DEBUG - https://api.airtable.com:443 "POST /v0/meta/bases/apptyeo8OtplQ8wAN/tables HTTP/1.1" 200 893
2025-07-10 17:32:10,520 - INFO - Created table: POS in base apptyeo8OtplQ8wAN
2025-07-10 17:32:10,521 - INFO - Successfully created table 'POS'
2025-07-10 17:32:10,532 - INFO - Creating table 'Meta Ads' in base apptyeo8OtplQ8wAN
2025-07-10 17:32:10,533 - INFO - Template has 14 fields
2025-07-10 17:32:10,533 - DEBUG - Starting new HTTPS connection (1): api.airtable.com:443
2025-07-10 17:32:11,322 - DEBUG - https://api.airtable.com:443 "POST /v0/meta/bases/apptyeo8OtplQ8wAN/tables HTTP/1.1" 200 837
2025-07-10 17:32:11,323 - INFO - Created table: Meta Ads in base apptyeo8OtplQ8wAN
2025-07-10 17:32:11,324 - INFO - Successfully created table 'Meta Ads'
2025-07-10 17:32:13,345 - DEBUG - Starting new HTTPS connection (1): api.airtable.com:443
2025-07-10 17:32:13,875 - DEBUG - https://api.airtable.com:443 "GET /v0/meta/bases/apptyeo8OtplQ8wAN/tables HTTP/1.1" 200 2647
2025-07-10 17:32:13,876 - INFO - Retrieved schema for base apptyeo8OtplQ8wAN: 4 tables
2025-07-10 17:32:13,889 - DEBUG - 🔍 Checking completeness for base: Quick Fix (app7ffftdM6e3yekG)
2025-07-10 17:32:13,890 - DEBUG -    📋 Existing tables: ['GHL', 'POS', 'Google Ads', 'Meta Ads', 'Meta Ads Summary', 'Meta Ads AT', 'Meta Ads Simplified', 'Meta Ads Performance']
2025-07-10 17:32:13,890 - DEBUG -    🔍 Looking for ghl table...
2025-07-10 17:32:13,890 - DEBUG -       ✅ Found exact match: GHL
2025-07-10 17:32:13,890 - DEBUG - 🔍 Checking fields for GHL (type: ghl)
2025-07-10 17:32:13,890 - DEBUG -    Required fields (10): ['Contact ID', 'First Name', 'Last Name', 'Email', 'Phone', 'Date Created', 'Lead Source', 'Status', 'Tags', 'Location']
2025-07-10 17:32:13,890 - DEBUG -    Existing fields (13): ['contact name', 'Location', 'phone', 'email', 'pipeline', 'stage', 'Lead Value', 'Date Created', 'Traffic Source', 'Channel', 'Conversion Event', 'Opportunity ID', 'Contact ID']
2025-07-10 17:32:13,891 - DEBUG -    ❌ Missing fields: ['First Name', 'Last Name', 'Email', 'Phone', 'Lead Source', 'Status', 'Tags']
2025-07-10 17:32:13,891 - DEBUG -       ⚠️ Table GHL missing 7 fields
2025-07-10 17:32:13,891 - DEBUG -    🔍 Looking for google_ads table...
2025-07-10 17:32:13,891 - DEBUG -       ✅ Found exact match: Google Ads
2025-07-10 17:32:13,891 - DEBUG - 🔍 Checking fields for Google Ads (type: google_ads)
2025-07-10 17:32:13,891 - DEBUG -    Required fields (12): ['Campaign ID', 'Campaign Name', 'Date', 'Impressions', 'Clicks', 'Cost', 'CTR', 'CPC', 'Conversions', 'Conversion Rate', 'Ad Group', 'Keywords']
2025-07-10 17:32:13,891 - DEBUG -    Existing fields (11): ['Date', 'Campaign ID', 'Campaign Name', 'Cost', 'Impressions', 'Clicks', 'Conversions', 'CTR', 'CPC', 'Conv. Rate', 'Cost per Conv.']
2025-07-10 17:32:13,892 - DEBUG -    ❌ Missing fields: ['Conversion Rate', 'Ad Group', 'Keywords']
2025-07-10 17:32:13,892 - DEBUG -       ⚠️ Table Google Ads missing 3 fields
2025-07-10 17:32:13,892 - DEBUG -    🔍 Looking for pos table...
2025-07-10 17:32:13,892 - DEBUG -       ✅ Found exact match: POS
2025-07-10 17:32:13,892 - DEBUG - 🔍 Checking fields for POS (type: pos)
2025-07-10 17:32:13,892 - DEBUG -    Required fields (10): ['Transaction ID', 'Customer Name', 'Customer Email', 'Customer Phone', 'Created', 'Total Amount', 'Payment Method', 'Items', 'Location', 'Status']
2025-07-10 17:32:13,893 - DEBUG -    Existing fields (9): ['Name', 'Company', 'Ticket Count', 'Ticket Amount', 'Location', 'Phone', 'Email', 'Created', 'Customer']
2025-07-10 17:32:13,893 - DEBUG -    ❌ Missing fields: ['Transaction ID', 'Customer Name', 'Customer Email', 'Customer Phone', 'Total Amount', 'Payment Method', 'Items', 'Status']
2025-07-10 17:32:13,893 - DEBUG -       ⚠️ Table POS missing 8 fields
2025-07-10 17:32:13,893 - DEBUG -    🔍 Looking for meta_ads table...
2025-07-10 17:32:13,893 - DEBUG -       ✅ Found exact match: Meta Ads
2025-07-10 17:32:13,894 - DEBUG - 🔍 Checking fields for Meta Ads (type: meta_ads)
2025-07-10 17:32:13,894 - DEBUG -    Required fields (12): ['Campaign ID', 'Campaign Name', 'Ad Set Name', 'Ad Name', 'Reporting starts', 'Reporting ends', 'Impressions', 'Clicks', 'Spend', 'CTR', 'CPC', 'Conversions']
2025-07-10 17:32:13,894 - DEBUG -    Existing fields (21): ['Reporting starts', 'Reporting ends', 'Ad name', 'Ad delivery', 'Ad Set Name', 'Bid', 'Bid type', 'Ad set budget', 'Ad set budget type', 'Last significant edit', 'Attribution setting', 'Results', 'Result indicator', 'Reach', 'Impressions', 'Cost per results', 'Quality ranking', 'Engagement rate ranking', 'Conversion rate ranking', 'Amount spent (USD)', 'Ends']
2025-07-10 17:32:13,894 - DEBUG -    ❌ Missing fields: ['Campaign ID', 'Campaign Name', 'Ad Name', 'Clicks', 'Spend', 'CTR', 'CPC', 'Conversions']
2025-07-10 17:32:13,894 - DEBUG -       ⚠️ Table Meta Ads missing 8 fields
2025-07-10 17:32:13,894 - DEBUG -    📊 Results: Missing tables: 0, Incomplete tables: 4
2025-07-10 17:32:13,895 - DEBUG -    🎯 Base complete: False
2025-07-10 17:32:13,895 - DEBUG - 🔍 Checking completeness for base: Fix My Gadget (appHschkYyi0nRLZR)
2025-07-10 17:32:13,895 - DEBUG -    📋 Existing tables: ['GHL', 'Google Ads', 'POS', 'Meta Ads']
2025-07-10 17:32:13,895 - DEBUG -    🔍 Looking for ghl table...
2025-07-10 17:32:13,895 - DEBUG -       ✅ Found exact match: GHL
2025-07-10 17:32:13,896 - DEBUG - 🔍 Checking fields for GHL (type: ghl)
2025-07-10 17:32:13,896 - DEBUG -    Required fields (10): ['Contact ID', 'First Name', 'Last Name', 'Email', 'Phone', 'Date Created', 'Lead Source', 'Status', 'Tags', 'Location']
2025-07-10 17:32:13,896 - DEBUG -    Existing fields (13): ['contact name', 'Location', 'phone', 'email', 'pipeline', 'stage', 'Lead Value', 'Date Created', 'Traffic Source', 'Channel', 'Conversion Event', 'Opportunity ID', 'Contact ID']
2025-07-10 17:32:13,896 - DEBUG -    ❌ Missing fields: ['First Name', 'Last Name', 'Email', 'Phone', 'Lead Source', 'Status', 'Tags']
2025-07-10 17:32:13,896 - DEBUG -       ⚠️ Table GHL missing 7 fields
2025-07-10 17:32:13,897 - DEBUG -    🔍 Looking for google_ads table...
2025-07-10 17:32:13,897 - DEBUG -       ✅ Found exact match: Google Ads
2025-07-10 17:32:13,897 - DEBUG - 🔍 Checking fields for Google Ads (type: google_ads)
2025-07-10 17:32:13,897 - DEBUG -    Required fields (12): ['Campaign ID', 'Campaign Name', 'Date', 'Impressions', 'Clicks', 'Cost', 'CTR', 'CPC', 'Conversions', 'Conversion Rate', 'Ad Group', 'Keywords']
2025-07-10 17:32:13,897 - DEBUG -    Existing fields (11): ['Campaign Name', 'Date', 'Impressions', 'Clicks', 'Cost', 'CTR', 'CPC', 'Conversions', 'Conversion Rate', 'Ad Group', 'Keywords']
2025-07-10 17:32:13,897 - DEBUG -    ❌ Missing fields: ['Campaign ID']
2025-07-10 17:32:13,897 - DEBUG -       ⚠️ Table Google Ads missing 1 fields
2025-07-10 17:32:13,897 - DEBUG -    🔍 Looking for pos table...
2025-07-10 17:32:13,897 - DEBUG -       ✅ Found exact match: POS
2025-07-10 17:32:13,897 - DEBUG - 🔍 Checking fields for POS (type: pos)
2025-07-10 17:32:13,897 - DEBUG -    Required fields (10): ['Transaction ID', 'Customer Name', 'Customer Email', 'Customer Phone', 'Created', 'Total Amount', 'Payment Method', 'Items', 'Location', 'Status']
2025-07-10 17:32:13,897 - DEBUG -    Existing fields (10): ['Transaction ID', 'Customer Name', 'Customer Email', 'Customer Phone', 'Created', 'Sale Amount', 'Payment Method', 'Service Type', 'Location', 'Notes']
2025-07-10 17:32:13,898 - DEBUG -    ❌ Missing fields: ['Total Amount', 'Items', 'Status']
2025-07-10 17:32:13,898 - DEBUG -       ⚠️ Table POS missing 3 fields
2025-07-10 17:32:13,898 - DEBUG -    🔍 Looking for meta_ads table...
2025-07-10 17:32:13,898 - DEBUG -       ✅ Found exact match: Meta Ads
2025-07-10 17:32:13,898 - DEBUG - 🔍 Checking fields for Meta Ads (type: meta_ads)
2025-07-10 17:32:13,898 - DEBUG -    Required fields (12): ['Campaign ID', 'Campaign Name', 'Ad Set Name', 'Ad Name', 'Reporting starts', 'Reporting ends', 'Impressions', 'Clicks', 'Spend', 'CTR', 'CPC', 'Conversions']
2025-07-10 17:32:13,898 - DEBUG -    Existing fields (14): ['Campaign Name', 'Ad Set Name', 'Ad Name', 'Reporting ends', 'Impressions', 'Clicks', 'Spend', 'CTR', 'CPC', 'CPM', 'Conversions', 'Conversion Rate', 'Platform', 'Objective']
2025-07-10 17:32:13,898 - DEBUG -    ❌ Missing fields: ['Campaign ID', 'Reporting starts']
2025-07-10 17:32:13,898 - DEBUG -       ⚠️ Table Meta Ads missing 2 fields
2025-07-10 17:32:13,898 - DEBUG -    📊 Results: Missing tables: 0, Incomplete tables: 4
2025-07-10 17:32:13,899 - DEBUG -    🎯 Base complete: False
2025-07-10 17:32:13,899 - DEBUG - 🔍 Checking completeness for base: Owl Repairs (app7wu1MtoDXK0UBN)
2025-07-10 17:32:13,899 - DEBUG -    📋 Existing tables: ['GHL', 'Google Ads Data', 'POS Sales Data', 'Meta Ads Data']
2025-07-10 17:32:13,899 - DEBUG -    🔍 Looking for ghl table...
2025-07-10 17:32:13,899 - DEBUG -       ✅ Found exact match: GHL
2025-07-10 17:32:13,899 - DEBUG - 🔍 Checking fields for GHL (type: ghl)
2025-07-10 17:32:13,899 - DEBUG -    Required fields (10): ['Contact ID', 'First Name', 'Last Name', 'Email', 'Phone', 'Date Created', 'Lead Source', 'Status', 'Tags', 'Location']
2025-07-10 17:32:13,899 - DEBUG -    Existing fields (15): ['contact name', 'Location', 'phone', 'email', 'pipeline', 'stage', 'Lead Value', 'Date Created', 'Traffic Source', 'Channel', 'Conversion Event', 'Opportunity ID', 'Contact ID', 'Field 13', 'Field 14']
2025-07-10 17:32:13,899 - DEBUG -    ❌ Missing fields: ['First Name', 'Last Name', 'Email', 'Phone', 'Lead Source', 'Status', 'Tags']
2025-07-10 17:32:13,899 - DEBUG -       ⚠️ Table GHL missing 7 fields
2025-07-10 17:32:13,899 - DEBUG -    🔍 Looking for google_ads table...
2025-07-10 17:32:13,900 - DEBUG -       ✅ Found partial match: Google Ads Data (contains 'google ads')
2025-07-10 17:32:13,900 - DEBUG - 🔍 Checking fields for Google Ads Data (type: google_ads)
2025-07-10 17:32:13,900 - DEBUG -    Required fields (12): ['Campaign ID', 'Campaign Name', 'Date', 'Impressions', 'Clicks', 'Cost', 'CTR', 'CPC', 'Conversions', 'Conversion Rate', 'Ad Group', 'Keywords']
2025-07-10 17:32:13,900 - DEBUG -    Existing fields (11): ['Campaign Name', 'Date', 'Impressions', 'Clicks', 'Cost', 'CTR', 'CPC', 'Conversions', 'Conversion Rate', 'Ad Group', 'Keywords']
2025-07-10 17:32:13,900 - DEBUG -    ❌ Missing fields: ['Campaign ID']
2025-07-10 17:32:13,900 - DEBUG -       ⚠️ Table Google Ads Data missing 1 fields
2025-07-10 17:32:13,900 - DEBUG -    🔍 Looking for pos table...
2025-07-10 17:32:13,900 - DEBUG -       ✅ Found partial match: POS Sales Data (contains 'pos')
2025-07-10 17:32:13,900 - DEBUG - 🔍 Checking fields for POS Sales Data (type: pos)
2025-07-10 17:32:13,900 - DEBUG -    Required fields (10): ['Transaction ID', 'Customer Name', 'Customer Email', 'Customer Phone', 'Created', 'Total Amount', 'Payment Method', 'Items', 'Location', 'Status']
2025-07-10 17:32:13,900 - DEBUG -    Existing fields (10): ['Transaction ID', 'Customer Name', 'Customer Email', 'Customer Phone', 'Created', 'Sale Amount', 'Payment Method', 'Service Type', 'Location', 'Notes']
2025-07-10 17:32:13,900 - DEBUG -    ❌ Missing fields: ['Total Amount', 'Items', 'Status']
2025-07-10 17:32:13,900 - DEBUG -       ⚠️ Table POS Sales Data missing 3 fields
2025-07-10 17:32:13,900 - DEBUG -    🔍 Looking for meta_ads table...
2025-07-10 17:32:13,901 - DEBUG -       ✅ Found partial match: Meta Ads Data (contains 'meta ads')
2025-07-10 17:32:13,901 - DEBUG - 🔍 Checking fields for Meta Ads Data (type: meta_ads)
2025-07-10 17:32:13,901 - DEBUG -    Required fields (12): ['Campaign ID', 'Campaign Name', 'Ad Set Name', 'Ad Name', 'Reporting starts', 'Reporting ends', 'Impressions', 'Clicks', 'Spend', 'CTR', 'CPC', 'Conversions']
2025-07-10 17:32:13,901 - DEBUG -    Existing fields (14): ['Campaign Name', 'Ad Set Name', 'Ad Name', 'Reporting ends', 'Impressions', 'Clicks', 'Spend', 'CTR', 'CPC', 'CPM', 'Conversions', 'Conversion Rate', 'Platform', 'Objective']
2025-07-10 17:32:13,901 - DEBUG -    ❌ Missing fields: ['Campaign ID', 'Reporting starts']
2025-07-10 17:32:13,901 - DEBUG -       ⚠️ Table Meta Ads Data missing 2 fields
2025-07-10 17:32:13,901 - DEBUG -    📊 Results: Missing tables: 0, Incomplete tables: 4
2025-07-10 17:32:13,901 - DEBUG -    🎯 Base complete: False
2025-07-10 17:32:13,901 - DEBUG - 🔍 Checking completeness for base: iGenius Repair (apptyeo8OtplQ8wAN)
2025-07-10 17:32:13,901 - DEBUG -    📋 Existing tables: ['GHL', 'Google Ads', 'POS', 'Meta Ads']
2025-07-10 17:32:13,901 - DEBUG -    🔍 Looking for ghl table...
2025-07-10 17:32:13,902 - DEBUG -       ✅ Found exact match: GHL
2025-07-10 17:32:13,902 - DEBUG - 🔍 Checking fields for GHL (type: ghl)
2025-07-10 17:32:13,902 - DEBUG -    Required fields (10): ['Contact ID', 'First Name', 'Last Name', 'Email', 'Phone', 'Date Created', 'Lead Source', 'Status', 'Tags', 'Location']
2025-07-10 17:32:13,902 - DEBUG -    Existing fields (13): ['contact name', 'Location', 'phone', 'email', 'pipeline', 'stage', 'Lead Value', 'Date Created', 'Traffic Source', 'Channel', 'Conversion Event', 'Opportunity ID', 'Contact ID']
2025-07-10 17:32:13,902 - DEBUG -    ❌ Missing fields: ['First Name', 'Last Name', 'Email', 'Phone', 'Lead Source', 'Status', 'Tags']
2025-07-10 17:32:13,902 - DEBUG -       ⚠️ Table GHL missing 7 fields
2025-07-10 17:32:13,902 - DEBUG -    🔍 Looking for google_ads table...
2025-07-10 17:32:13,902 - DEBUG -       ✅ Found exact match: Google Ads
2025-07-10 17:32:13,902 - DEBUG - 🔍 Checking fields for Google Ads (type: google_ads)
2025-07-10 17:32:13,902 - DEBUG -    Required fields (12): ['Campaign ID', 'Campaign Name', 'Date', 'Impressions', 'Clicks', 'Cost', 'CTR', 'CPC', 'Conversions', 'Conversion Rate', 'Ad Group', 'Keywords']
2025-07-10 17:32:13,902 - DEBUG -    Existing fields (12): ['Campaign ID', 'Campaign Name', 'Date', 'Impressions', 'Clicks', 'Cost', 'CTR', 'CPC', 'Conversions', 'Conversion Rate', 'Ad Group', 'Keywords']
2025-07-10 17:32:13,902 - DEBUG -    ✅ All required fields present
2025-07-10 17:32:13,902 - DEBUG -       ✅ Table Google Ads has all required fields
2025-07-10 17:32:13,903 - DEBUG -    🔍 Looking for pos table...
2025-07-10 17:32:13,903 - DEBUG -       ✅ Found exact match: POS
2025-07-10 17:32:13,903 - DEBUG - 🔍 Checking fields for POS (type: pos)
2025-07-10 17:32:13,903 - DEBUG -    Required fields (10): ['Transaction ID', 'Customer Name', 'Customer Email', 'Customer Phone', 'Created', 'Total Amount', 'Payment Method', 'Items', 'Location', 'Status']
2025-07-10 17:32:13,903 - DEBUG -    Existing fields (10): ['Transaction ID', 'Customer Name', 'Customer Email', 'Customer Phone', 'Created', 'Sale Amount', 'Payment Method', 'Service Type', 'Location', 'Notes']
2025-07-10 17:32:13,903 - DEBUG -    ❌ Missing fields: ['Total Amount', 'Items', 'Status']
2025-07-10 17:32:13,903 - DEBUG -       ⚠️ Table POS missing 3 fields
2025-07-10 17:32:13,903 - DEBUG -    🔍 Looking for meta_ads table...
2025-07-10 17:32:13,903 - DEBUG -       ✅ Found exact match: Meta Ads
2025-07-10 17:32:13,903 - DEBUG - 🔍 Checking fields for Meta Ads (type: meta_ads)
2025-07-10 17:32:13,903 - DEBUG -    Required fields (12): ['Campaign ID', 'Campaign Name', 'Ad Set Name', 'Ad Name', 'Reporting starts', 'Reporting ends', 'Impressions', 'Clicks', 'Spend', 'CTR', 'CPC', 'Conversions']
2025-07-10 17:32:13,904 - DEBUG -    Existing fields (14): ['Campaign Name', 'Ad Set Name', 'Ad Name', 'Reporting ends', 'Impressions', 'Clicks', 'Spend', 'CTR', 'CPC', 'CPM', 'Conversions', 'Conversion Rate', 'Platform', 'Objective']
2025-07-10 17:32:13,904 - DEBUG -    ❌ Missing fields: ['Campaign ID', 'Reporting starts']
2025-07-10 17:32:13,904 - DEBUG -       ⚠️ Table Meta Ads missing 2 fields
2025-07-10 17:32:13,904 - DEBUG -    📊 Results: Missing tables: 0, Incomplete tables: 3
2025-07-10 17:32:13,904 - DEBUG -    🎯 Base complete: False
2025-07-10 17:32:13,904 - DEBUG - 🔍 Checking completeness for base: Ready Set Repair (appJWDQxzECcXvWz7)
2025-07-10 17:32:13,904 - DEBUG -    📋 Existing tables: ['GHL', 'Google Ads', 'POS', 'Meta Ads']
2025-07-10 17:32:13,904 - DEBUG -    🔍 Looking for ghl table...
2025-07-10 17:32:13,904 - DEBUG -       ✅ Found exact match: GHL
2025-07-10 17:32:13,904 - DEBUG - 🔍 Checking fields for GHL (type: ghl)
2025-07-10 17:32:13,904 - DEBUG -    Required fields (10): ['Contact ID', 'First Name', 'Last Name', 'Email', 'Phone', 'Date Created', 'Lead Source', 'Status', 'Tags', 'Location']
2025-07-10 17:32:13,904 - DEBUG -    Existing fields (8): ['Contact Name', 'Email', 'Phone', 'Date Created', 'Lead Source', 'Status', 'Tags', 'Notes']
2025-07-10 17:32:13,904 - DEBUG -    ❌ Missing fields: ['Contact ID', 'First Name', 'Last Name', 'Location']
2025-07-10 17:32:13,905 - DEBUG -       ⚠️ Table GHL missing 4 fields
2025-07-10 17:32:13,905 - DEBUG -    🔍 Looking for google_ads table...
2025-07-10 17:32:13,905 - DEBUG -       ✅ Found exact match: Google Ads
2025-07-10 17:32:13,905 - DEBUG - 🔍 Checking fields for Google Ads (type: google_ads)
2025-07-10 17:32:13,905 - DEBUG -    Required fields (12): ['Campaign ID', 'Campaign Name', 'Date', 'Impressions', 'Clicks', 'Cost', 'CTR', 'CPC', 'Conversions', 'Conversion Rate', 'Ad Group', 'Keywords']
2025-07-10 17:32:13,905 - DEBUG -    Existing fields (11): ['Campaign Name', 'Date', 'Impressions', 'Clicks', 'Cost', 'CTR', 'CPC', 'Conversions', 'Conversion Rate', 'Ad Group', 'Keywords']
2025-07-10 17:32:13,905 - DEBUG -    ❌ Missing fields: ['Campaign ID']
2025-07-10 17:32:13,905 - DEBUG -       ⚠️ Table Google Ads missing 1 fields
2025-07-10 17:32:13,905 - DEBUG -    🔍 Looking for pos table...
2025-07-10 17:32:13,905 - DEBUG -       ✅ Found exact match: POS
2025-07-10 17:32:13,905 - DEBUG - 🔍 Checking fields for POS (type: pos)
2025-07-10 17:32:13,905 - DEBUG -    Required fields (10): ['Transaction ID', 'Customer Name', 'Customer Email', 'Customer Phone', 'Created', 'Total Amount', 'Payment Method', 'Items', 'Location', 'Status']
2025-07-10 17:32:13,905 - DEBUG -    Existing fields (10): ['Transaction ID', 'Customer Name', 'Customer Email', 'Customer Phone', 'Created', 'Sale Amount', 'Payment Method', 'Service Type', 'Location', 'Notes']
2025-07-10 17:32:13,906 - DEBUG -    ❌ Missing fields: ['Total Amount', 'Items', 'Status']
2025-07-10 17:32:13,906 - DEBUG -       ⚠️ Table POS missing 3 fields
2025-07-10 17:32:13,906 - DEBUG -    🔍 Looking for meta_ads table...
2025-07-10 17:32:13,906 - DEBUG -       ✅ Found exact match: Meta Ads
2025-07-10 17:32:13,906 - DEBUG - 🔍 Checking fields for Meta Ads (type: meta_ads)
2025-07-10 17:32:13,906 - DEBUG -    Required fields (12): ['Campaign ID', 'Campaign Name', 'Ad Set Name', 'Ad Name', 'Reporting starts', 'Reporting ends', 'Impressions', 'Clicks', 'Spend', 'CTR', 'CPC', 'Conversions']
2025-07-10 17:32:13,906 - DEBUG -    Existing fields (14): ['Campaign Name', 'Ad Set Name', 'Ad Name', 'Reporting ends', 'Impressions', 'Clicks', 'Spend', 'CTR', 'CPC', 'CPM', 'Conversions', 'Conversion Rate', 'Platform', 'Objective']
2025-07-10 17:32:13,906 - DEBUG -    ❌ Missing fields: ['Campaign ID', 'Reporting starts']
2025-07-10 17:32:13,906 - DEBUG -       ⚠️ Table Meta Ads missing 2 fields
2025-07-10 17:32:13,906 - DEBUG -    📊 Results: Missing tables: 0, Incomplete tables: 4
2025-07-10 17:32:13,906 - DEBUG -    🎯 Base complete: False
2025-07-10 17:32:13,906 - DEBUG - 🔍 Checking completeness for base: MobileComm (app5ldOYdhNAOaeOF)
2025-07-10 17:32:13,907 - DEBUG -    📋 Existing tables: ['GHL', 'Google Ads', 'POS', 'Meta Ads']
2025-07-10 17:32:13,907 - DEBUG -    🔍 Looking for ghl table...
2025-07-10 17:32:13,907 - DEBUG -       ✅ Found exact match: GHL
2025-07-10 17:32:13,907 - DEBUG - 🔍 Checking fields for GHL (type: ghl)
2025-07-10 17:32:13,907 - DEBUG -    Required fields (10): ['Contact ID', 'First Name', 'Last Name', 'Email', 'Phone', 'Date Created', 'Lead Source', 'Status', 'Tags', 'Location']
2025-07-10 17:32:13,907 - DEBUG -    Existing fields (8): ['Contact Name', 'Email', 'Phone', 'Date Created', 'Lead Source', 'Status', 'Tags', 'Notes']
2025-07-10 17:32:13,907 - DEBUG -    ❌ Missing fields: ['Contact ID', 'First Name', 'Last Name', 'Location']
2025-07-10 17:32:13,907 - DEBUG -       ⚠️ Table GHL missing 4 fields
2025-07-10 17:32:13,908 - DEBUG -    🔍 Looking for google_ads table...
2025-07-10 17:32:13,908 - DEBUG -       ✅ Found exact match: Google Ads
2025-07-10 17:32:13,908 - DEBUG - 🔍 Checking fields for Google Ads (type: google_ads)
2025-07-10 17:32:13,908 - DEBUG -    Required fields (12): ['Campaign ID', 'Campaign Name', 'Date', 'Impressions', 'Clicks', 'Cost', 'CTR', 'CPC', 'Conversions', 'Conversion Rate', 'Ad Group', 'Keywords']
2025-07-10 17:32:13,908 - DEBUG -    Existing fields (11): ['Campaign Name', 'Date', 'Impressions', 'Clicks', 'Cost', 'CTR', 'CPC', 'Conversions', 'Conversion Rate', 'Ad Group', 'Keywords']
2025-07-10 17:32:13,908 - DEBUG -    ❌ Missing fields: ['Campaign ID']
2025-07-10 17:32:13,908 - DEBUG -       ⚠️ Table Google Ads missing 1 fields
2025-07-10 17:32:13,908 - DEBUG -    🔍 Looking for pos table...
2025-07-10 17:32:13,909 - DEBUG -       ✅ Found exact match: POS
2025-07-10 17:32:13,909 - DEBUG - 🔍 Checking fields for POS (type: pos)
2025-07-10 17:32:13,909 - DEBUG -    Required fields (10): ['Transaction ID', 'Customer Name', 'Customer Email', 'Customer Phone', 'Created', 'Total Amount', 'Payment Method', 'Items', 'Location', 'Status']
2025-07-10 17:32:13,909 - DEBUG -    Existing fields (10): ['Transaction ID', 'Customer Name', 'Customer Email', 'Customer Phone', 'Created', 'Sale Amount', 'Payment Method', 'Service Type', 'Location', 'Notes']
2025-07-10 17:32:13,909 - DEBUG -    ❌ Missing fields: ['Total Amount', 'Items', 'Status']
2025-07-10 17:32:13,909 - DEBUG -       ⚠️ Table POS missing 3 fields
2025-07-10 17:32:13,909 - DEBUG -    🔍 Looking for meta_ads table...
2025-07-10 17:32:13,909 - DEBUG -       ✅ Found exact match: Meta Ads
2025-07-10 17:32:13,909 - DEBUG - 🔍 Checking fields for Meta Ads (type: meta_ads)
2025-07-10 17:32:13,910 - DEBUG -    Required fields (12): ['Campaign ID', 'Campaign Name', 'Ad Set Name', 'Ad Name', 'Reporting starts', 'Reporting ends', 'Impressions', 'Clicks', 'Spend', 'CTR', 'CPC', 'Conversions']
2025-07-10 17:32:13,910 - DEBUG -    Existing fields (14): ['Campaign Name', 'Ad Set Name', 'Ad Name', 'Reporting ends', 'Impressions', 'Clicks', 'Spend', 'CTR', 'CPC', 'CPM', 'Conversions', 'Conversion Rate', 'Platform', 'Objective']
2025-07-10 17:32:13,910 - DEBUG -    ❌ Missing fields: ['Campaign ID', 'Reporting starts']
2025-07-10 17:32:13,910 - DEBUG -       ⚠️ Table Meta Ads missing 2 fields
2025-07-10 17:32:13,910 - DEBUG -    📊 Results: Missing tables: 0, Incomplete tables: 4
2025-07-10 17:32:13,910 - DEBUG -    🎯 Base complete: False
2025-07-10 17:32:13,910 - DEBUG - 🔍 Checking completeness for base: Cellular Zone (app9JgRBZC2GNlaKM)
2025-07-10 17:32:13,910 - DEBUG -    📋 Existing tables: ['GHL', 'Google Ads', 'POS', 'Meta Ads']
2025-07-10 17:32:13,911 - DEBUG -    🔍 Looking for ghl table...
2025-07-10 17:32:13,911 - DEBUG -       ✅ Found exact match: GHL
2025-07-10 17:32:13,911 - DEBUG - 🔍 Checking fields for GHL (type: ghl)
2025-07-10 17:32:13,911 - DEBUG -    Required fields (10): ['Contact ID', 'First Name', 'Last Name', 'Email', 'Phone', 'Date Created', 'Lead Source', 'Status', 'Tags', 'Location']
2025-07-10 17:32:13,911 - DEBUG -    Existing fields (8): ['Contact Name', 'Email', 'Phone', 'Date Created', 'Lead Source', 'Status', 'Tags', 'Notes']
2025-07-10 17:32:13,911 - DEBUG -    ❌ Missing fields: ['Contact ID', 'First Name', 'Last Name', 'Location']
2025-07-10 17:32:13,911 - DEBUG -       ⚠️ Table GHL missing 4 fields
2025-07-10 17:32:13,912 - DEBUG -    🔍 Looking for google_ads table...
2025-07-10 17:32:13,912 - DEBUG -       ✅ Found exact match: Google Ads
2025-07-10 17:32:13,912 - DEBUG - 🔍 Checking fields for Google Ads (type: google_ads)
2025-07-10 17:32:13,912 - DEBUG -    Required fields (12): ['Campaign ID', 'Campaign Name', 'Date', 'Impressions', 'Clicks', 'Cost', 'CTR', 'CPC', 'Conversions', 'Conversion Rate', 'Ad Group', 'Keywords']
2025-07-10 17:32:13,912 - DEBUG -    Existing fields (11): ['Campaign Name', 'Date', 'Impressions', 'Clicks', 'Cost', 'CTR', 'CPC', 'Conversions', 'Conversion Rate', 'Ad Group', 'Keywords']
2025-07-10 17:32:13,912 - DEBUG -    ❌ Missing fields: ['Campaign ID']
2025-07-10 17:32:13,912 - DEBUG -       ⚠️ Table Google Ads missing 1 fields
2025-07-10 17:32:13,912 - DEBUG -    🔍 Looking for pos table...
2025-07-10 17:32:13,912 - DEBUG -       ✅ Found exact match: POS
2025-07-10 17:32:13,913 - DEBUG - 🔍 Checking fields for POS (type: pos)
2025-07-10 17:32:13,913 - DEBUG -    Required fields (10): ['Transaction ID', 'Customer Name', 'Customer Email', 'Customer Phone', 'Created', 'Total Amount', 'Payment Method', 'Items', 'Location', 'Status']
2025-07-10 17:32:13,913 - DEBUG -    Existing fields (10): ['Transaction ID', 'Customer Name', 'Customer Email', 'Customer Phone', 'Created', 'Sale Amount', 'Payment Method', 'Service Type', 'Location', 'Notes']
2025-07-10 17:32:13,913 - DEBUG -    ❌ Missing fields: ['Total Amount', 'Items', 'Status']
2025-07-10 17:32:13,913 - DEBUG -       ⚠️ Table POS missing 3 fields
2025-07-10 17:32:13,913 - DEBUG -    🔍 Looking for meta_ads table...
2025-07-10 17:32:13,913 - DEBUG -       ✅ Found exact match: Meta Ads
2025-07-10 17:32:13,913 - DEBUG - 🔍 Checking fields for Meta Ads (type: meta_ads)
2025-07-10 17:32:13,913 - DEBUG -    Required fields (12): ['Campaign ID', 'Campaign Name', 'Ad Set Name', 'Ad Name', 'Reporting starts', 'Reporting ends', 'Impressions', 'Clicks', 'Spend', 'CTR', 'CPC', 'Conversions']
2025-07-10 17:32:13,914 - DEBUG -    Existing fields (14): ['Campaign Name', 'Ad Set Name', 'Ad Name', 'Reporting ends', 'Impressions', 'Clicks', 'Spend', 'CTR', 'CPC', 'CPM', 'Conversions', 'Conversion Rate', 'Platform', 'Objective']
2025-07-10 17:32:13,914 - DEBUG -    ❌ Missing fields: ['Campaign ID', 'Reporting starts']
2025-07-10 17:32:13,914 - DEBUG -       ⚠️ Table Meta Ads missing 2 fields
2025-07-10 17:32:13,914 - DEBUG -    📊 Results: Missing tables: 0, Incomplete tables: 4
2025-07-10 17:32:13,914 - DEBUG -    🎯 Base complete: False
2025-07-10 17:32:13,914 - DEBUG - 🔍 Checking completeness for base: Gadget Repair LV (appL4rTljQgGkjTtp)
2025-07-10 17:32:13,914 - DEBUG -    📋 Existing tables: ['GHL', 'Google Ads', 'POS', 'Meta Ads']
2025-07-10 17:32:13,914 - DEBUG -    🔍 Looking for ghl table...
2025-07-10 17:32:13,915 - DEBUG -       ✅ Found exact match: GHL
2025-07-10 17:32:13,915 - DEBUG - 🔍 Checking fields for GHL (type: ghl)
2025-07-10 17:32:13,915 - DEBUG -    Required fields (10): ['Contact ID', 'First Name', 'Last Name', 'Email', 'Phone', 'Date Created', 'Lead Source', 'Status', 'Tags', 'Location']
2025-07-10 17:32:13,915 - DEBUG -    Existing fields (8): ['Contact Name', 'Email', 'Phone', 'Date Created', 'Lead Source', 'Status', 'Tags', 'Notes']
2025-07-10 17:32:13,915 - DEBUG -    ❌ Missing fields: ['Contact ID', 'First Name', 'Last Name', 'Location']
2025-07-10 17:32:13,915 - DEBUG -       ⚠️ Table GHL missing 4 fields
2025-07-10 17:32:13,915 - DEBUG -    🔍 Looking for google_ads table...
2025-07-10 17:32:13,915 - DEBUG -       ✅ Found exact match: Google Ads
2025-07-10 17:32:13,915 - DEBUG - 🔍 Checking fields for Google Ads (type: google_ads)
2025-07-10 17:32:13,915 - DEBUG -    Required fields (12): ['Campaign ID', 'Campaign Name', 'Date', 'Impressions', 'Clicks', 'Cost', 'CTR', 'CPC', 'Conversions', 'Conversion Rate', 'Ad Group', 'Keywords']
2025-07-10 17:32:13,915 - DEBUG -    Existing fields (11): ['Campaign Name', 'Date', 'Impressions', 'Clicks', 'Cost', 'CTR', 'CPC', 'Conversions', 'Conversion Rate', 'Ad Group', 'Keywords']
2025-07-10 17:32:13,916 - DEBUG -    ❌ Missing fields: ['Campaign ID']
2025-07-10 17:32:13,916 - DEBUG -       ⚠️ Table Google Ads missing 1 fields
2025-07-10 17:32:13,916 - DEBUG -    🔍 Looking for pos table...
2025-07-10 17:32:13,916 - DEBUG -       ✅ Found exact match: POS
2025-07-10 17:32:13,916 - DEBUG - 🔍 Checking fields for POS (type: pos)
2025-07-10 17:32:13,916 - DEBUG -    Required fields (10): ['Transaction ID', 'Customer Name', 'Customer Email', 'Customer Phone', 'Created', 'Total Amount', 'Payment Method', 'Items', 'Location', 'Status']
2025-07-10 17:32:13,916 - DEBUG -    Existing fields (10): ['Transaction ID', 'Customer Name', 'Customer Email', 'Customer Phone', 'Created', 'Sale Amount', 'Payment Method', 'Service Type', 'Location', 'Notes']
2025-07-10 17:32:13,916 - DEBUG -    ❌ Missing fields: ['Total Amount', 'Items', 'Status']
2025-07-10 17:32:13,916 - DEBUG -       ⚠️ Table POS missing 3 fields
2025-07-10 17:32:13,916 - DEBUG -    🔍 Looking for meta_ads table...
2025-07-10 17:32:13,917 - DEBUG -       ✅ Found exact match: Meta Ads
2025-07-10 17:32:13,917 - DEBUG - 🔍 Checking fields for Meta Ads (type: meta_ads)
2025-07-10 17:32:13,917 - DEBUG -    Required fields (12): ['Campaign ID', 'Campaign Name', 'Ad Set Name', 'Ad Name', 'Reporting starts', 'Reporting ends', 'Impressions', 'Clicks', 'Spend', 'CTR', 'CPC', 'Conversions']
2025-07-10 17:32:13,917 - DEBUG -    Existing fields (14): ['Campaign Name', 'Ad Set Name', 'Ad Name', 'Reporting ends', 'Impressions', 'Clicks', 'Spend', 'CTR', 'CPC', 'CPM', 'Conversions', 'Conversion Rate', 'Platform', 'Objective']
2025-07-10 17:32:13,917 - DEBUG -    ❌ Missing fields: ['Campaign ID', 'Reporting starts']
2025-07-10 17:32:13,917 - DEBUG -       ⚠️ Table Meta Ads missing 2 fields
2025-07-10 17:32:13,917 - DEBUG -    📊 Results: Missing tables: 0, Incomplete tables: 4
2025-07-10 17:32:13,917 - DEBUG -    🎯 Base complete: False
2025-07-10 17:32:13,918 - DEBUG - 🔍 Checking completeness for base: ⚠️ iGenius Repair (apptyeo8OtplQ8wAN)
2025-07-10 17:32:13,918 - DEBUG -    📋 Existing tables: ['GHL']
2025-07-10 17:32:13,918 - DEBUG -    🔍 Looking for ghl table...
2025-07-10 17:32:13,918 - DEBUG -       ✅ Found exact match: GHL
2025-07-10 17:32:13,918 - DEBUG - 🔍 Checking fields for GHL (type: ghl)
2025-07-10 17:32:13,918 - DEBUG -    Required fields (10): ['Contact ID', 'First Name', 'Last Name', 'Email', 'Phone', 'Date Created', 'Lead Source', 'Status', 'Tags', 'Location']
2025-07-10 17:32:13,918 - DEBUG -    Existing fields (13): ['contact name', 'Location', 'phone', 'email', 'pipeline', 'stage', 'Lead Value', 'Date Created', 'Traffic Source', 'Channel', 'Conversion Event', 'Opportunity ID', 'Contact ID']
2025-07-10 17:32:13,918 - DEBUG -    ❌ Missing fields: ['First Name', 'Last Name', 'Email', 'Phone', 'Lead Source', 'Status', 'Tags']
2025-07-10 17:32:13,918 - DEBUG -       ⚠️ Table GHL missing 7 fields
2025-07-10 17:32:13,918 - DEBUG -    🔍 Looking for google_ads table...
2025-07-10 17:32:13,918 - DEBUG -       ❌ No google_ads table found
2025-07-10 17:32:13,919 - DEBUG -    🔍 Looking for pos table...
2025-07-10 17:32:13,919 - DEBUG -       ❌ No pos table found
2025-07-10 17:32:13,919 - DEBUG -    🔍 Looking for meta_ads table...
2025-07-10 17:32:13,919 - DEBUG -       ❌ No meta_ads table found
2025-07-10 17:32:13,919 - DEBUG -    📊 Results: Missing tables: 3, Incomplete tables: 1
2025-07-10 17:32:13,919 - DEBUG -    🎯 Base complete: False
2025-07-10 17:34:12,646 - INFO - Application cleanup completed
2025-07-10 17:34:15,409 - INFO - Application cleanup completed
2025-07-10 17:39:26,613 - INFO - Found 8 accessible Airtable bases
2025-07-10 17:39:27,411 - INFO - Retrieved schema for base app7ffftdM6e3yekG: 8 tables
2025-07-10 17:39:29,401 - INFO - Retrieved schema for base appHschkYyi0nRLZR: 4 tables
2025-07-10 17:39:31,451 - INFO - Retrieved schema for base app7wu1MtoDXK0UBN: 4 tables
2025-07-10 17:39:32,145 - INFO - Retrieved schema for base apptyeo8OtplQ8wAN: 3 tables
2025-07-10 17:39:34,126 - INFO - Retrieved schema for base appJWDQxzECcXvWz7: 4 tables
2025-07-10 17:39:35,951 - INFO - Retrieved schema for base app5ldOYdhNAOaeOF: 4 tables
2025-07-10 17:39:38,029 - INFO - Retrieved schema for base app9JgRBZC2GNlaKM: 4 tables
2025-07-10 17:39:40,155 - INFO - Retrieved schema for base appL4rTljQgGkjTtp: 4 tables
2025-07-10 17:39:58,392 - INFO - Retrieved schema for base apptyeo8OtplQ8wAN: 3 tables
2025-07-10 17:40:25,134 - INFO - Creating table 'Google Ads' in base apptyeo8OtplQ8wAN
2025-07-10 17:40:25,134 - INFO - Template has 11 fields
2025-07-10 17:40:25,927 - INFO - Created table: Google Ads in base apptyeo8OtplQ8wAN
2025-07-10 17:40:25,929 - INFO - Successfully created table 'Google Ads'
2025-07-10 17:40:28,554 - INFO - Retrieved schema for base apptyeo8OtplQ8wAN: 4 tables
2025-07-10 17:44:40,898 - INFO - Retrieved schema for base apptyeo8OtplQ8wAN: 4 tables
2025-07-10 17:46:04,951 - INFO - Application cleanup completed
2025-07-10 17:48:29,800 - INFO - Application cleanup completed
2025-07-10 17:48:53,697 - INFO - Application cleanup completed
2025-07-10 17:52:27,615 - INFO - Application cleanup completed
2025-07-10 18:08:58,975 - INFO - Found 8 accessible Airtable bases
2025-07-10 18:09:06,325 - INFO - Retrieved schema for base app7ffftdM6e3yekG: 8 tables
2025-07-10 18:09:08,290 - INFO - Retrieved schema for base appHschkYyi0nRLZR: 4 tables
2025-07-10 18:09:10,288 - INFO - Retrieved schema for base app7wu1MtoDXK0UBN: 4 tables
2025-07-10 18:09:12,101 - INFO - Retrieved schema for base apptyeo8OtplQ8wAN: 4 tables
2025-07-10 18:09:13,934 - INFO - Retrieved schema for base appJWDQxzECcXvWz7: 4 tables
2025-07-10 18:09:15,763 - INFO - Retrieved schema for base app5ldOYdhNAOaeOF: 4 tables
2025-07-10 18:09:17,804 - INFO - Retrieved schema for base app9JgRBZC2GNlaKM: 4 tables
2025-07-10 18:09:19,732 - INFO - Retrieved schema for base appL4rTljQgGkjTtp: 4 tables
2025-07-10 18:09:32,292 - INFO - Retrieved schema for base appHschkYyi0nRLZR: 4 tables
2025-07-10 18:09:34,456 - INFO - Retrieved schema for base appHschkYyi0nRLZR: 4 tables
2025-07-10 18:09:43,502 - INFO - Retrieved schema for base app7wu1MtoDXK0UBN: 4 tables
2025-07-10 18:10:02,165 - INFO - Retrieved schema for base apptyeo8OtplQ8wAN: 4 tables
2025-07-10 18:10:21,562 - INFO - Retrieved schema for base apptyeo8OtplQ8wAN: 4 tables
2025-07-10 18:10:24,335 - INFO - Retrieved schema for base apptyeo8OtplQ8wAN: 4 tables
2025-07-10 18:10:39,332 - INFO - Retrieved schema for base apptyeo8OtplQ8wAN: 4 tables
2025-07-10 18:11:00,219 - INFO - Retrieved schema for base apptyeo8OtplQ8wAN: 4 tables
2025-07-10 18:14:54,669 - INFO - Retrieved schema for base app7ffftdM6e3yekG: 8 tables
2025-07-10 18:14:56,605 - INFO - Retrieved schema for base app7ffftdM6e3yekG: 8 tables
2025-07-10 18:16:06,491 - INFO - Retrieved schema for base app7ffftdM6e3yekG: 8 tables
2025-07-10 18:16:38,321 - INFO - Retrieved schema for base app7ffftdM6e3yekG: 8 tables
2025-07-10 19:07:06,668 - INFO - Application cleanup completed
2025-07-10 19:44:10,206 - INFO - Application cleanup completed
